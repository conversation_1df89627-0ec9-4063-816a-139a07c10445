/*******************************************************************************
 * Size: 30 px
 * Bpp: 1
 * Opts: --no-compress --no-prefilter --force-fast-kern-format --font fa-light-300.ttf --format lvgl --lv-include lvgl.h --bpp 1 -o font_awesome_30_1.c --size 30 -r 0xf5a4,0xf118,0xf59b,0xf588,0xe384,0xf556,0xf5b3,0xf584,0xf579,0xe36b,0xe375,0xe39b,0xf4da,0xe398,0xe392,0xe372,0xf598,0xe409,0xe38d,0xe3a4,0xe36d,0xf240,0xf241,0xf242,0xf243,0xf244,0xf377,0xf376,0xf1eb,0xf6ab,0xf6aa,0xf6ac,0xf012,0xf68f,0xf68e,0xf68d,0xf68c,0xf695,0xf028,0xf6a8,0xf027,0xf6a9,0xf001,0xf00c,0xf00d,0xf011,0xf013,0xf1f8,0xf015,0xf03e,0xf044,0xf048,0xf051,0xf04b,0xf04c,0xf04d,0xf060,0xf061,0xf062,0xf063,0xf071,0xf0f3,0xf3c5,0xf0ac,0xf124,0xf7c2,0xf293,0xf075,0xe1ec,0xf007,0xe04b,0xf019
 ******************************************************************************/

#ifdef LV_LVGL_H_INCLUDE_SIMPLE
#include "lvgl.h"
#else
#include "lvgl.h"
#endif

#ifndef FONT_AWESOME_30_1
#define FONT_AWESOME_30_1 1
#endif

#if FONT_AWESOME_30_1

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+E04B "" */
    0x0, 0xc, 0x0, 0x0, 0x3, 0x0, 0x0, 0x0,
    0xc0, 0x0, 0x0, 0x30, 0x0, 0x3, 0xff, 0xf0,
    0x1, 0xff, 0xfe, 0x0, 0xe4, 0x0, 0xc0, 0x37,
    0x87, 0x90, 0xcd, 0xf3, 0xe6, 0xf3, 0x4c, 0xcd,
    0xbc, 0xdf, 0x3f, 0x6f, 0x33, 0x87, 0x9b, 0xcc,
    0x0, 0x6, 0xf3, 0x0, 0x1, 0xbc, 0xcc, 0xcc,
    0x6c, 0x33, 0x33, 0x10, 0xe, 0xcc, 0xcc, 0x1,
    0xff, 0xff, 0x0, 0x3f, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xff, 0xfe, 0xf, 0xff, 0xff, 0xc7,
    0x0, 0x0, 0x3b, 0x80, 0x0, 0x7, 0xc3, 0xff,
    0xf8, 0xf1, 0xff, 0xff, 0x3c, 0x6c, 0xcc, 0xcf,
    0x1b, 0x33, 0x33, 0xff, 0xff, 0xff, 0xdf, 0xff,
    0xff, 0xe0,

    /* U+E1EC "" */
    0x0, 0x63, 0xc, 0x0, 0x0, 0xc6, 0x18, 0x0,
    0x1, 0x8c, 0x30, 0x0, 0x3, 0x18, 0x60, 0x0,
    0x3f, 0xff, 0xf8, 0x0, 0xff, 0xff, 0xf8, 0x3,
    0x80, 0x0, 0x38, 0x6, 0x0, 0x0, 0x30, 0xc,
    0x0, 0x0, 0x61, 0xf8, 0x0, 0x0, 0xff, 0xf0,
    0x0, 0x1, 0xf8, 0x60, 0x40, 0xc3, 0x0, 0xc0,
    0xc1, 0x86, 0x1, 0x83, 0x83, 0xc, 0x3, 0x7,
    0x86, 0x18, 0x7e, 0x1b, 0xc, 0x3f, 0xfc, 0x36,
    0x18, 0x7e, 0x18, 0x7e, 0x30, 0xc0, 0x31, 0xfc,
    0x61, 0x80, 0x63, 0x18, 0xc3, 0xf, 0xc4, 0x10,
    0x7, 0xff, 0x80, 0x0, 0xf, 0xc3, 0x0, 0x0,
    0x18, 0x6, 0x0, 0x0, 0x30, 0xe, 0x0, 0x0,
    0xe0, 0xf, 0xff, 0xff, 0x80, 0xf, 0xff, 0xfe,
    0x0, 0x3, 0x18, 0x60, 0x0, 0x6, 0x30, 0xc0,
    0x0, 0xc, 0x61, 0x80, 0x0, 0x18, 0xc3, 0x0,
    0x0,

    /* U+E36B "" */
    0x0, 0x1f, 0xe0, 0x0, 0x3, 0xff, 0xf0, 0x0,
    0x1e, 0x1, 0xe0, 0x1, 0xe0, 0x1, 0xe0, 0xe,
    0x0, 0x1, 0xc0, 0x73, 0xc0, 0xf3, 0x81, 0x9f,
    0x3, 0xe6, 0xc, 0xe0, 0x1, 0xcc, 0x72, 0x0,
    0x1, 0x39, 0x80, 0x0, 0x0, 0x66, 0x0, 0x0,
    0x1, 0xb0, 0x0, 0x0, 0x3, 0xc0, 0x60, 0x18,
    0xf, 0x1, 0x80, 0x60, 0x3c, 0x0, 0x0, 0x0,
    0xf0, 0x0, 0x0, 0x3, 0xc0, 0x0, 0x0, 0xf,
    0x0, 0x1e, 0x0, 0x3c, 0x0, 0xfc, 0x0, 0xd8,
    0x7, 0x38, 0x6, 0x60, 0x18, 0x60, 0x19, 0xc0,
    0x61, 0x80, 0xe3, 0x1, 0x86, 0x3, 0x6, 0x7,
    0x38, 0x18, 0x1c, 0xf, 0xc0, 0xe0, 0x38, 0x1e,
    0x7, 0x0, 0x78, 0x0, 0x78, 0x0, 0x78, 0x7,
    0x80, 0x0, 0xff, 0xfc, 0x0, 0x0, 0x7f, 0x80,
    0x0,

    /* U+E36D "" */
    0x0, 0x1f, 0xe0, 0x0, 0x3, 0xff, 0xf0, 0x0,
    0x1e, 0x1, 0xe0, 0x1, 0xe0, 0x1, 0xe0, 0xe,
    0x0, 0x1, 0xc0, 0x70, 0x0, 0x3, 0x81, 0x80,
    0x0, 0x6, 0xc, 0x0, 0x0, 0xc, 0x70, 0x0,
    0x0, 0x39, 0x80, 0x0, 0x0, 0x66, 0x0, 0x0,
    0x1, 0xb0, 0x1c, 0x6, 0x3, 0xc0, 0x70, 0x38,
    0xf, 0x0, 0x80, 0x40, 0x3c, 0x0, 0x0, 0x0,
    0xf0, 0x0, 0x0, 0x3, 0xc0, 0x0, 0x0, 0xf,
    0x0, 0x0, 0x0, 0x3c, 0x0, 0xf, 0xc0, 0xd8,
    0x1, 0xff, 0x6, 0x60, 0x1f, 0x0, 0x19, 0xc0,
    0xe0, 0x0, 0xe3, 0x6, 0x0, 0x3, 0x6, 0x10,
    0x0, 0x18, 0x1c, 0x0, 0x0, 0xe0, 0x38, 0x0,
    0x7, 0x0, 0x78, 0x0, 0x78, 0x0, 0x78, 0x7,
    0x80, 0x0, 0xff, 0xfc, 0x0, 0x0, 0x7f, 0x80,
    0x0,

    /* U+E372 "" */
    0x0, 0x1f, 0xe0, 0x0, 0x3, 0xff, 0xe0, 0x0,
    0x1e, 0x1, 0xe0, 0x1, 0xe0, 0x1, 0xe0, 0xe,
    0x0, 0x1, 0xc0, 0x70, 0x0, 0x3, 0x81, 0x80,
    0x0, 0x6, 0xc, 0x0, 0x0, 0xc, 0x70, 0x70,
    0x38, 0x39, 0x83, 0xc0, 0xf0, 0x66, 0x1c, 0x0,
    0x61, 0xb0, 0x60, 0x1, 0xc3, 0xc1, 0x0, 0x2,
    0xf, 0x0, 0x0, 0x0, 0x3c, 0x0, 0x0, 0x0,
    0xf0, 0x0, 0x0, 0x3, 0xc0, 0x0, 0x0, 0xf,
    0x4, 0x0, 0xc, 0x3c, 0x18, 0x0, 0x70, 0xd8,
    0x70, 0x3, 0x86, 0x60, 0x70, 0x3e, 0x19, 0xc0,
    0xff, 0xf8, 0xe3, 0x0, 0xfe, 0x63, 0x6, 0x0,
    0x19, 0x98, 0x1c, 0x0, 0x66, 0x60, 0x38, 0x1,
    0x98, 0x0, 0x78, 0x6, 0x60, 0x0, 0x78, 0x19,
    0x80, 0x0, 0xff, 0x6, 0x0, 0x0, 0x7c, 0x18,
    0x0,

    /* U+E375 "" */
    0x0, 0x1f, 0xe0, 0x0, 0x3, 0xff, 0xf0, 0x0,
    0x1e, 0x1, 0xe0, 0x1, 0xe0, 0x1, 0xe0, 0xe,
    0xe0, 0x1d, 0xc0, 0x7f, 0x80, 0x7f, 0x81, 0xf0,
    0x0, 0x3e, 0xd, 0x80, 0x0, 0x6c, 0x70, 0xe0,
    0x3e, 0x39, 0x8f, 0xe1, 0xfc, 0x66, 0x31, 0x86,
    0x39, 0xb1, 0x93, 0x32, 0x63, 0xc6, 0xec, 0xdd,
    0x8f, 0x1b, 0xb3, 0x36, 0x3c, 0x61, 0xcc, 0x18,
    0xf0, 0xfe, 0x1d, 0xe3, 0xc1, 0xf0, 0x7f, 0xf,
    0x1, 0x0, 0x70, 0x3c, 0x0, 0xfc, 0x0, 0xd8,
    0x7, 0xf8, 0x6, 0x60, 0x38, 0x70, 0x19, 0xc0,
    0xc0, 0xc0, 0xe3, 0x3, 0xff, 0x83, 0x6, 0xf,
    0xfc, 0x18, 0x1c, 0x0, 0x0, 0xe0, 0x38, 0x0,
    0x7, 0x0, 0x78, 0x0, 0x78, 0x0, 0x78, 0x7,
    0x80, 0x0, 0xff, 0xfc, 0x0, 0x0, 0x7f, 0x80,
    0x0,

    /* U+E384 "" */
    0x0, 0x1f, 0xe0, 0x0, 0x3, 0xff, 0xf0, 0x0,
    0x1e, 0x1, 0xe0, 0x1, 0xe0, 0x1, 0xe0, 0xe,
    0x0, 0x1, 0xc0, 0x70, 0x0, 0x3, 0x81, 0x80,
    0x0, 0x6, 0xc, 0x8, 0x4, 0xc, 0x70, 0x60,
    0x18, 0x39, 0x83, 0x0, 0x30, 0x66, 0x3c, 0x0,
    0xf1, 0xb1, 0xc0, 0x0, 0xe3, 0xc6, 0x0, 0x1,
    0x8f, 0x0, 0x0, 0x0, 0x3c, 0x0, 0x0, 0x0,
    0xf0, 0x0, 0x0, 0x3, 0xc3, 0x18, 0x63, 0xf,
    0xf, 0xe1, 0xfc, 0x3c, 0x1e, 0x1, 0xe0, 0xd8,
    0x0, 0x0, 0x6, 0x60, 0x0, 0x0, 0x19, 0xc0,
    0x0, 0x0, 0xe3, 0x0, 0xfe, 0x3, 0x6, 0x3,
    0xf8, 0x18, 0x1c, 0x0, 0x0, 0xe0, 0x38, 0x0,
    0x7, 0x0, 0x78, 0x0, 0x78, 0x0, 0x78, 0x7,
    0x80, 0x0, 0xff, 0xfc, 0x0, 0x0, 0x7f, 0x80,
    0x0,

    /* U+E38D "" */
    0x0, 0x1f, 0xe0, 0x7c, 0x3, 0xff, 0xe1, 0xf0,
    0x1e, 0x1, 0xc3, 0x81, 0xe0, 0x0, 0x1e, 0xe,
    0x0, 0x0, 0xfc, 0x70, 0x0, 0x7c, 0x1, 0x80,
    0x1, 0xe0, 0xc, 0x0, 0x3, 0x80, 0x70, 0x0,
    0x1c, 0x11, 0x80, 0x0, 0x78, 0x66, 0x0, 0x1,
    0xe1, 0xb0, 0x0, 0x0, 0x3, 0xc0, 0x0, 0x0,
    0xf, 0x0, 0x0, 0x0, 0x3c, 0x31, 0x86, 0x30,
    0xf0, 0xfe, 0x1f, 0xc3, 0xc1, 0xe0, 0x1e, 0xf,
    0x0, 0x0, 0x0, 0x3c, 0x0, 0x0, 0x0, 0xd8,
    0x0, 0xe0, 0x6, 0x60, 0xf, 0xe0, 0x19, 0xc0,
    0x39, 0x80, 0xe3, 0x0, 0xc6, 0x3, 0x6, 0x3,
    0x98, 0x18, 0x1c, 0x7, 0xc0, 0xe0, 0x38, 0x4,
    0x7, 0x0, 0x78, 0x0, 0x78, 0x0, 0x78, 0x7,
    0x80, 0x0, 0xff, 0xfc, 0x0, 0x0, 0x7f, 0x80,
    0x0,

    /* U+E392 "" */
    0x0, 0x1f, 0xe0, 0x0, 0x3, 0xff, 0xf0, 0x0,
    0x1e, 0x1, 0xe0, 0x1, 0xe0, 0x1, 0xe0, 0xe,
    0x0, 0x1, 0xc0, 0x70, 0x0, 0x3, 0x81, 0x80,
    0x0, 0x6, 0xc, 0x0, 0x0, 0xc, 0x70, 0x0,
    0x0, 0x39, 0x80, 0x0, 0x0, 0x66, 0xf, 0x3,
    0xc1, 0xb0, 0x7f, 0x3f, 0x83, 0xc1, 0x8c, 0xc6,
    0xf, 0x0, 0x0, 0x0, 0x3c, 0x0, 0x0, 0x0,
    0xf0, 0x0, 0x0, 0x3, 0xc0, 0x0, 0x0, 0xf,
    0x0, 0x0, 0x0, 0x3c, 0x8, 0x0, 0x40, 0xd8,
    0x38, 0x7, 0x6, 0x60, 0x70, 0x38, 0x19, 0xc0,
    0xff, 0xc0, 0xe3, 0x0, 0xfc, 0x3, 0x6, 0x0,
    0x0, 0x18, 0x1c, 0x0, 0x0, 0xe0, 0x38, 0x0,
    0x7, 0x0, 0x78, 0x0, 0x78, 0x0, 0x78, 0x7,
    0x80, 0x0, 0xff, 0xfc, 0x0, 0x0, 0x7f, 0x80,
    0x0,

    /* U+E398 "" */
    0x0, 0x1f, 0xe0, 0x0, 0x3, 0xff, 0xf0, 0x0,
    0x1e, 0x1, 0xe0, 0x1, 0xe0, 0x0, 0xe0, 0xe,
    0x0, 0x1, 0xc0, 0x70, 0x0, 0x1, 0x81, 0xff,
    0x87, 0xfe, 0xf, 0xff, 0x3f, 0xfc, 0x70, 0xc7,
    0xc6, 0x39, 0xc7, 0x1e, 0x38, 0xe7, 0x3e, 0x79,
    0xf3, 0xbc, 0xf9, 0xe7, 0xcf, 0xf1, 0xc7, 0x8e,
    0x3f, 0x46, 0x1e, 0x38, 0xbd, 0xb0, 0xcd, 0xc6,
    0xf7, 0xff, 0x3f, 0xfb, 0xcf, 0xf8, 0x7f, 0xcf,
    0x0, 0x0, 0x0, 0x3e, 0x0, 0x0, 0x1, 0xd8,
    0x20, 0x1, 0x6, 0x60, 0xe0, 0x1c, 0x19, 0xc1,
    0xc0, 0xe0, 0xe3, 0x3, 0xff, 0x3, 0x6, 0x3,
    0xf0, 0x18, 0x1c, 0x0, 0x0, 0xe0, 0x38, 0x0,
    0x7, 0x0, 0x78, 0x0, 0x78, 0x0, 0x78, 0x7,
    0x80, 0x0, 0xff, 0xfc, 0x0, 0x0, 0x7f, 0x80,
    0x0,

    /* U+E39B "" */
    0x0, 0x1f, 0xe0, 0x0, 0x3, 0xff, 0xe0, 0x0,
    0x1e, 0x1, 0xe0, 0x1, 0xe0, 0x1, 0xe0, 0xe,
    0x0, 0x1, 0xc0, 0x70, 0x0, 0x3, 0x81, 0x9f,
    0x80, 0x6, 0xc, 0xff, 0x0, 0xc, 0x70, 0xe,
    0x0, 0x39, 0x80, 0x0, 0x0, 0x66, 0x6, 0x0,
    0x1, 0xb0, 0x18, 0xe, 0x3, 0xc0, 0x0, 0x38,
    0xf, 0x0, 0x0, 0x40, 0x3c, 0x0, 0x0, 0x0,
    0xf0, 0x4, 0x0, 0x3, 0xc7, 0x3e, 0x0, 0xf,
    0x3e, 0x1f, 0x0, 0x3c, 0xd8, 0x1f, 0x0, 0xd3,
    0x63, 0xec, 0x6, 0xd, 0xfc, 0x10, 0x18, 0x37,
    0xc1, 0xc0, 0xe0, 0xd8, 0x1e, 0x3, 0x3, 0x0,
    0xe0, 0x18, 0xc, 0x3, 0x0, 0xe0, 0x30, 0x8,
    0x7, 0x0, 0xc0, 0x60, 0x78, 0x3, 0x83, 0x7,
    0x80, 0x7, 0xfc, 0x7c, 0x0, 0xf, 0xe1, 0x80,
    0x0,

    /* U+E3A4 "" */
    0x0, 0x1f, 0xe0, 0x0, 0x3, 0xff, 0xe0, 0x0,
    0x1e, 0x1, 0xe0, 0x1, 0xe0, 0x1, 0xe0, 0xe,
    0x0, 0xf1, 0xc0, 0x70, 0xf, 0xe3, 0x81, 0x80,
    0x3c, 0xc6, 0xc, 0x1, 0xf9, 0xc, 0x70, 0x7,
    0xc6, 0x39, 0x86, 0x18, 0x10, 0x66, 0x7f, 0x30,
    0xc1, 0xb1, 0x8c, 0xfe, 0x3, 0xcd, 0x98, 0xf0,
    0xf, 0x36, 0x60, 0xe, 0x3c, 0xe3, 0x0, 0xf8,
    0xf1, 0xfc, 0xe, 0x63, 0xc3, 0xe0, 0xe1, 0x8f,
    0x0, 0xe, 0x6, 0x3c, 0x0, 0x70, 0x38, 0xd8,
    0x7, 0x0, 0xc6, 0x60, 0x7f, 0xe7, 0x19, 0xc3,
    0xff, 0xf8, 0xe3, 0x7, 0x33, 0xc3, 0x6, 0x18,
    0xc6, 0x18, 0x1c, 0x60, 0x18, 0xe0, 0x39, 0x80,
    0x67, 0x0, 0x77, 0x3, 0xb8, 0x0, 0x4e, 0x1c,
    0x80, 0x0, 0x1f, 0xe0, 0x0, 0x0, 0x3f, 0x0,
    0x0,

    /* U+E409 "" */
    0x0, 0x1f, 0xe0, 0x0, 0x3, 0xff, 0xf0, 0x0,
    0x1e, 0x1, 0xe0, 0x1, 0xe0, 0x1, 0xe0, 0xe,
    0x0, 0x1, 0xc0, 0x70, 0x0, 0x3, 0x81, 0x80,
    0x0, 0x6, 0xc, 0xf8, 0x1f, 0xc, 0x77, 0xf0,
    0xfe, 0x39, 0xbb, 0xe6, 0x7c, 0x66, 0xc7, 0x98,
    0xb1, 0xb3, 0x6, 0x60, 0xc3, 0xcf, 0xf9, 0xff,
    0xf, 0x1f, 0xc3, 0xf8, 0x3c, 0x0, 0x0, 0x0,
    0xf0, 0x0, 0x0, 0x3, 0xcf, 0xff, 0xff, 0xf,
    0x3f, 0xff, 0xfe, 0x3c, 0xc0, 0x0, 0x30, 0xd9,
    0x80, 0x3c, 0xc6, 0x66, 0x3, 0xff, 0x19, 0xcc,
    0x1c, 0x78, 0xe3, 0x3c, 0x61, 0xe3, 0x6, 0x7d,
    0xf, 0x18, 0x1c, 0x7f, 0xf0, 0xe0, 0x38, 0x7f,
    0x87, 0x0, 0x78, 0x0, 0x78, 0x0, 0x78, 0x7,
    0x80, 0x0, 0xff, 0xfc, 0x0, 0x0, 0x7f, 0x80,
    0x0,

    /* U+F001 "" */
    0x0, 0x0, 0x0, 0x1c, 0x0, 0x0, 0x3, 0xf0,
    0x0, 0x0, 0xfe, 0xc0, 0x0, 0x1f, 0xc3, 0x0,
    0x3, 0xf0, 0xc, 0x0, 0xfe, 0x0, 0x30, 0x7,
    0xc0, 0x0, 0xc0, 0x18, 0x0, 0x7, 0x0, 0x60,
    0x0, 0xfc, 0x1, 0x80, 0x1f, 0xb0, 0x6, 0x3,
    0xf0, 0xc0, 0x18, 0xfe, 0x3, 0x0, 0x7f, 0xc0,
    0xc, 0x1, 0xf0, 0x0, 0x30, 0x6, 0x0, 0x0,
    0xc0, 0x18, 0x0, 0x3, 0x0, 0x60, 0x0, 0xc,
    0x1, 0x80, 0xf, 0xb0, 0x6, 0x0, 0xff, 0xc0,
    0x18, 0x7, 0x7, 0x1f, 0x60, 0x18, 0xd, 0xff,
    0x80, 0x60, 0x37, 0x1e, 0x1, 0x80, 0xf0, 0x38,
    0x7, 0x6, 0xc0, 0x60, 0xf, 0xfb, 0x1, 0x80,
    0xf, 0x8c, 0xe, 0x0, 0x0, 0x18, 0x70, 0x0,
    0x0, 0x7f, 0xc0, 0x0, 0x0, 0x7c, 0x0, 0x0,
    0x0,

    /* U+F007 "" */
    0x0, 0x1f, 0x0, 0x0, 0x1f, 0xf0, 0x0, 0xe,
    0xe, 0x0, 0x7, 0x1, 0xc0, 0x1, 0x80, 0x30,
    0x0, 0xc0, 0x6, 0x0, 0x30, 0x1, 0x80, 0xc,
    0x0, 0x60, 0x3, 0x0, 0x18, 0x0, 0xc0, 0x6,
    0x0, 0x18, 0x3, 0x0, 0x7, 0x1, 0xc0, 0x0,
    0xe0, 0xe0, 0x0, 0x1f, 0xf0, 0x0, 0x1, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xfe, 0x0, 0x1f, 0xff,
    0xe0, 0x1e, 0x0, 0x1e, 0xe, 0x0, 0x1, 0xc3,
    0x0, 0x0, 0x31, 0x80, 0x0, 0x6, 0x60, 0x0,
    0x1, 0xb0, 0x0, 0x0, 0x3c, 0x0, 0x0, 0xf,
    0x0, 0x0, 0x3, 0xff, 0xff, 0xff, 0xdf, 0xff,
    0xff, 0xe0,

    /* U+F00C "" */
    0x0, 0x0, 0x0, 0x40, 0x0, 0x0, 0x18, 0x0,
    0x0, 0x6, 0x0, 0x0, 0x1, 0x80, 0x0, 0x0,
    0x60, 0x0, 0x0, 0x18, 0x0, 0x0, 0x6, 0x0,
    0x0, 0x1, 0x80, 0xc0, 0x0, 0x60, 0x1c, 0x0,
    0x18, 0x1, 0xc0, 0x6, 0x0, 0x1c, 0x1, 0x80,
    0x1, 0xc0, 0x60, 0x0, 0x1c, 0x18, 0x0, 0x1,
    0xc6, 0x0, 0x0, 0x1d, 0x80, 0x0, 0x1, 0xe0,
    0x0, 0x0, 0x18, 0x0, 0x0,

    /* U+F00D "" */
    0xc0, 0x0, 0x5c, 0x0, 0x19, 0xc0, 0x7, 0x1c,
    0x1, 0xc1, 0xc0, 0x70, 0x1c, 0x1c, 0x1, 0xc7,
    0x0, 0x1d, 0xc0, 0x1, 0xf0, 0x0, 0x1c, 0x0,
    0x7, 0x80, 0x1, 0xd8, 0x0, 0x71, 0x80, 0x1c,
    0x18, 0x7, 0x1, 0x81, 0xc0, 0x18, 0x70, 0x1,
    0x9c, 0x0, 0x1b, 0x0, 0x1, 0x80,

    /* U+F011 "" */
    0x0, 0x6, 0x0, 0x0, 0x0, 0x60, 0x0, 0x1,
    0x6, 0x8, 0x0, 0x38, 0x61, 0xc0, 0xe, 0x6,
    0x7, 0x1, 0xc0, 0x60, 0x38, 0x18, 0x6, 0x1,
    0x83, 0x0, 0x60, 0xc, 0x70, 0x6, 0x0, 0xe6,
    0x0, 0x60, 0x6, 0x60, 0x6, 0x0, 0x6c, 0x0,
    0x60, 0x3, 0xc0, 0x6, 0x0, 0x3c, 0x0, 0x60,
    0x3, 0xc0, 0x6, 0x0, 0x3c, 0x0, 0x60, 0x3,
    0xc0, 0x6, 0x0, 0x3c, 0x0, 0x0, 0x3, 0x60,
    0x0, 0x0, 0x66, 0x0, 0x0, 0x6, 0x70, 0x0,
    0x0, 0xe3, 0x0, 0x0, 0xc, 0x18, 0x0, 0x1,
    0x80, 0xc0, 0x0, 0x30, 0x7, 0x0, 0xe, 0x0,
    0x3c, 0x3, 0xc0, 0x0, 0xff, 0xf0, 0x0, 0x3,
    0xfc, 0x0,

    /* U+F012 "" */
    0x0, 0x0, 0x0, 0x3, 0x0, 0x0, 0x0, 0x3,
    0x0, 0x0, 0x0, 0x3, 0x0, 0x0, 0x0, 0x3,
    0x0, 0x0, 0x0, 0x3, 0x0, 0x0, 0x0, 0x3,
    0x0, 0x0, 0x3, 0x3, 0x0, 0x0, 0x3, 0x3,
    0x0, 0x0, 0x3, 0x3, 0x0, 0x0, 0x3, 0x3,
    0x0, 0x0, 0x3, 0x3, 0x0, 0x1, 0x83, 0x3,
    0x0, 0x1, 0x83, 0x3, 0x0, 0x1, 0x83, 0x3,
    0x0, 0x1, 0x83, 0x3, 0x0, 0x1, 0x83, 0x3,
    0x0, 0x1, 0x83, 0x3, 0x1, 0x81, 0x83, 0x3,
    0x1, 0x81, 0x83, 0x3, 0x1, 0x81, 0x83, 0x3,
    0x1, 0x81, 0x83, 0x3, 0x1, 0x81, 0x83, 0x3,
    0x1, 0x81, 0x83, 0x3, 0xc1, 0x81, 0x83, 0x3,
    0xc1, 0x81, 0x83, 0x3, 0xc1, 0x81, 0x83, 0x3,
    0xc1, 0x81, 0x83, 0x3, 0xc1, 0x81, 0x83, 0x3,
    0xc1, 0x81, 0x83, 0x3, 0xc1, 0x81, 0x83, 0x3,

    /* U+F013 "" */
    0x0, 0x1f, 0x80, 0x0, 0x3, 0xfc, 0x0, 0x0,
    0x30, 0xc0, 0x0, 0x3, 0xc, 0x0, 0x0, 0x20,
    0x40, 0x1, 0xce, 0x7, 0x18, 0x3f, 0xc0, 0x3f,
    0xc6, 0x38, 0x0, 0xc6, 0x60, 0x0, 0x0, 0x6c,
    0x0, 0x0, 0x3, 0xc0, 0x1f, 0x80, 0x36, 0x3,
    0xfc, 0x6, 0x30, 0x70, 0xe0, 0xc1, 0x86, 0x6,
    0x18, 0x18, 0x60, 0x61, 0x81, 0x86, 0x6, 0x18,
    0x18, 0x60, 0x61, 0x83, 0x3, 0xc, 0xc, 0x60,
    0x3f, 0xc0, 0x6c, 0x0, 0xf0, 0x3, 0xc0, 0x0,
    0x0, 0x3e, 0x0, 0x0, 0x6, 0x63, 0x0, 0xc,
    0x63, 0xfc, 0x3, 0xfc, 0x3c, 0xe0, 0x73, 0x80,
    0x6, 0x4, 0x0, 0x0, 0x30, 0xc0, 0x0, 0x3,
    0xc, 0x0, 0x0, 0x3f, 0xc0, 0x0, 0x1, 0xf8,
    0x0,

    /* U+F015 "" */
    0x0, 0x0, 0xc0, 0x0, 0x0, 0x0, 0x78, 0x0,
    0x0, 0x0, 0x3f, 0x0, 0x0, 0x0, 0x1c, 0xe0,
    0x0, 0x0, 0xe, 0x1c, 0x0, 0x0, 0x6, 0x1,
    0x80, 0x0, 0x7, 0x0, 0x38, 0x0, 0x3, 0x80,
    0x7, 0x0, 0x1, 0xc0, 0x0, 0xe0, 0x0, 0xe0,
    0x0, 0x1c, 0x0, 0x70, 0x0, 0x3, 0x80, 0x38,
    0x0, 0x0, 0x70, 0x1c, 0x0, 0x0, 0xe, 0xf,
    0x0, 0x0, 0x3, 0xce, 0xc0, 0x0, 0x0, 0xdf,
    0x30, 0x0, 0x0, 0x33, 0xc, 0x0, 0x0, 0xc,
    0x3, 0x1, 0xfe, 0x3, 0x0, 0xc0, 0xff, 0xc0,
    0xc0, 0x30, 0x30, 0x30, 0x30, 0xc, 0xc, 0xc,
    0xc, 0x3, 0x3, 0x3, 0x3, 0x0, 0xc0, 0xc0,
    0xc0, 0xc0, 0x30, 0x30, 0x30, 0x30, 0xc, 0xc,
    0xc, 0xc, 0x3, 0x3, 0x3, 0x3, 0x0, 0xc0,
    0xc0, 0xc0, 0xc0, 0x38, 0x30, 0x30, 0x70, 0x7,
    0xff, 0xff, 0xf8, 0x0, 0xff, 0xff, 0xfc, 0x0,

    /* U+F019 "" */
    0x0, 0x3, 0x0, 0x0, 0x0, 0xc, 0x0, 0x0,
    0x0, 0x30, 0x0, 0x0, 0x0, 0xc0, 0x0, 0x0,
    0x3, 0x0, 0x0, 0x0, 0xc, 0x0, 0x0, 0x0,
    0x30, 0x0, 0x0, 0x0, 0xc0, 0x0, 0x0, 0x3,
    0x0, 0x0, 0x0, 0xc, 0x0, 0x0, 0x0, 0x30,
    0x0, 0x0, 0x0, 0xc0, 0x0, 0x1, 0x3, 0x2,
    0x0, 0x6, 0xc, 0x18, 0x0, 0x1c, 0x30, 0xe0,
    0x0, 0x38, 0xc7, 0x0, 0x0, 0x73, 0x38, 0x0,
    0x0, 0xed, 0xc0, 0x3, 0xf9, 0xfe, 0x7f, 0x1f,
    0xf3, 0xf3, 0xfe, 0xe0, 0x7, 0x80, 0x1f, 0x0,
    0xc, 0x0, 0x3c, 0x0, 0x0, 0x0, 0xf0, 0x0,
    0x0, 0x63, 0xc0, 0x0, 0x1, 0x8f, 0x0, 0x0,
    0x0, 0x3c, 0x0, 0x0, 0x0, 0xf8, 0x0, 0x0,
    0x7, 0x7f, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff,
    0xc0,

    /* U+F027 "" */
    0x0, 0x0, 0xc0, 0x0, 0x0, 0x78, 0x0, 0x0,
    0x36, 0x0, 0x0, 0x19, 0x80, 0x0, 0x1c, 0x60,
    0x0, 0xe, 0x18, 0x0, 0x7, 0x6, 0x0, 0x7f,
    0x81, 0x80, 0x7f, 0xc0, 0x63, 0x38, 0x0, 0x18,
    0xec, 0x0, 0x6, 0x1f, 0x0, 0x1, 0x83, 0xc0,
    0x0, 0x60, 0xf0, 0x0, 0x18, 0x3c, 0x0, 0x6,
    0xf, 0x0, 0x1, 0x86, 0xe0, 0x0, 0x63, 0x9f,
    0xf0, 0x18, 0x3, 0xfe, 0x6, 0x0, 0x1, 0xc1,
    0x80, 0x0, 0x38, 0x60, 0x0, 0x7, 0x18, 0x0,
    0x0, 0xe6, 0x0, 0x0, 0xd, 0x80, 0x0, 0x1,
    0xe0, 0x0, 0x0, 0x30, 0x0,

    /* U+F028 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0x0,
    0xe, 0x0, 0x0, 0x1e, 0x0, 0x1c, 0x0, 0x0,
    0xd8, 0x0, 0x30, 0x0, 0x6, 0x60, 0x0, 0x60,
    0x0, 0x71, 0x80, 0xc0, 0xc0, 0x3, 0x86, 0x1,
    0x83, 0x0, 0x1c, 0x18, 0x3, 0x6, 0x1f, 0xe0,
    0x60, 0xe, 0x19, 0xff, 0x1, 0x8c, 0x18, 0x6e,
    0x0, 0x6, 0x38, 0x70, 0xf0, 0x0, 0x18, 0x70,
    0xc3, 0xc0, 0x0, 0x60, 0xc3, 0xf, 0x0, 0x1,
    0x83, 0xc, 0x3c, 0x0, 0x6, 0xc, 0x30, 0xf0,
    0x0, 0x18, 0x30, 0xc3, 0xc0, 0x0, 0x61, 0x83,
    0xf, 0x80, 0x1, 0x8e, 0x18, 0x37, 0xfc, 0x6,
    0x0, 0x61, 0x8f, 0xf8, 0x18, 0x3, 0x6, 0x0,
    0x70, 0x60, 0x1c, 0x38, 0x0, 0xe1, 0x80, 0xe0,
    0xc0, 0x1, 0xc6, 0x1, 0x6, 0x0, 0x3, 0x98,
    0x0, 0x38, 0x0, 0x3, 0x60, 0x1, 0xc0, 0x0,
    0x7, 0x80, 0x6, 0x0, 0x0, 0xc, 0x0, 0x10,
    0x0,

    /* U+F03E "" */
    0x3f, 0xff, 0xff, 0xf1, 0xff, 0xff, 0xff, 0xee,
    0x0, 0x0, 0x1, 0xf0, 0x20, 0x0, 0x3, 0xc3,
    0xe0, 0x0, 0xf, 0xf, 0x80, 0x0, 0x3c, 0x36,
    0x0, 0x0, 0xf0, 0xf8, 0x0, 0x3, 0xc1, 0xc0,
    0x0, 0xf, 0x0, 0x0, 0x1c, 0x3c, 0x0, 0x0,
    0xf8, 0xf0, 0x0, 0xe, 0x33, 0xc1, 0x0, 0x70,
    0x6f, 0x1e, 0x3, 0x80, 0xfc, 0xec, 0x1c, 0x1,
    0xf7, 0x18, 0xe0, 0x3, 0xd8, 0x37, 0x0, 0xf,
    0xc0, 0x78, 0x0, 0x3e, 0x1, 0xc0, 0x0, 0xf0,
    0x6, 0x0, 0x3, 0xc0, 0x30, 0x0, 0xf, 0x1,
    0x80, 0x0, 0x3c, 0xc, 0x0, 0x0, 0xf8, 0x60,
    0x0, 0x7, 0x7f, 0xff, 0xff, 0xf8, 0xff, 0xff,
    0xff, 0xc0,

    /* U+F044 "" */
    0x0, 0x0, 0x0, 0xe0, 0x0, 0x0, 0xf, 0xc0,
    0x0, 0x0, 0x71, 0x8f, 0xfe, 0x3, 0x3, 0x7f,
    0xf8, 0x1e, 0xf, 0x80, 0x0, 0xdc, 0x7c, 0x0,
    0x6, 0x3b, 0xb0, 0x0, 0x30, 0x7c, 0xc0, 0x1,
    0x80, 0xe3, 0x0, 0xc, 0x7, 0xc, 0x0, 0x60,
    0x38, 0x30, 0x3, 0x81, 0xc0, 0xc0, 0x1c, 0xe,
    0x3, 0x0, 0xe0, 0x70, 0xc, 0x7, 0x3, 0x80,
    0x30, 0x38, 0x1c, 0x0, 0xc0, 0xc0, 0xe0, 0x3,
    0x3, 0x7, 0x3, 0xc, 0xc, 0x38, 0xc, 0x30,
    0x33, 0xc0, 0x30, 0xc1, 0xfe, 0x0, 0xc3, 0x7,
    0xc0, 0x3, 0xc, 0x0, 0x0, 0xc, 0x30, 0x0,
    0x0, 0x30, 0xc0, 0x0, 0x0, 0xc3, 0x0, 0x0,
    0x3, 0xc, 0x0, 0x0, 0xc, 0x38, 0x0, 0x0,
    0x70, 0x7f, 0xff, 0xff, 0x80, 0xff, 0xff, 0xfc,
    0x0,

    /* U+F048 "" */
    0xc0, 0xf, 0x80, 0x3f, 0x0, 0xde, 0x7, 0x3c,
    0x1c, 0x78, 0x70, 0xf1, 0xc1, 0xee, 0x3, 0xf8,
    0x7, 0xe0, 0xf, 0x80, 0x1f, 0x0, 0x3f, 0x0,
    0x7f, 0x0, 0xf3, 0x1, 0xe3, 0x83, 0xc3, 0x87,
    0x83, 0x8f, 0x3, 0x9e, 0x1, 0xfc, 0x1, 0xf8,
    0x1, 0xc0,

    /* U+F04B "" */
    0x0, 0x0, 0x0, 0xf0, 0x0, 0x3, 0xf8, 0x0,
    0x6, 0x3c, 0x0, 0xc, 0x1c, 0x0, 0x18, 0x1e,
    0x0, 0x30, 0xf, 0x0, 0x60, 0x7, 0x0, 0xc0,
    0x7, 0x81, 0x80, 0x3, 0xc3, 0x0, 0x3, 0xc6,
    0x0, 0x1, 0xec, 0x0, 0x0, 0xf8, 0x0, 0x0,
    0xf0, 0x0, 0x1, 0xe0, 0x0, 0xe, 0xc0, 0x0,
    0x79, 0x80, 0x1, 0xc3, 0x0, 0xf, 0x6, 0x0,
    0x78, 0xc, 0x1, 0xc0, 0x18, 0xf, 0x0, 0x30,
    0x78, 0x0, 0x61, 0xc0, 0x0, 0xcf, 0x0, 0x1,
    0xf8, 0x0, 0x1, 0xc0, 0x0, 0x0,

    /* U+F04C "" */
    0x7e, 0x1f, 0xdf, 0xe7, 0xff, 0xc, 0xc1, 0xe1,
    0x98, 0x3c, 0x33, 0x7, 0x86, 0x60, 0xf0, 0xcc,
    0x1e, 0x19, 0x83, 0xc3, 0x30, 0x78, 0x66, 0xf,
    0xc, 0xc1, 0xe1, 0x98, 0x3c, 0x33, 0x7, 0x86,
    0x60, 0xf0, 0xcc, 0x1e, 0x19, 0x83, 0xc3, 0x30,
    0x78, 0x66, 0xf, 0xc, 0xc1, 0xe1, 0x98, 0x3f,
    0xf3, 0xfe, 0xfc, 0x3f, 0x80,

    /* U+F04D "" */
    0x3f, 0xff, 0xf8, 0xff, 0xff, 0xfb, 0x80, 0x0,
    0x3e, 0x0, 0x0, 0x3c, 0x0, 0x0, 0x78, 0x0,
    0x0, 0xf0, 0x0, 0x1, 0xe0, 0x0, 0x3, 0xc0,
    0x0, 0x7, 0x80, 0x0, 0xf, 0x0, 0x0, 0x1e,
    0x0, 0x0, 0x3c, 0x0, 0x0, 0x78, 0x0, 0x0,
    0xf0, 0x0, 0x1, 0xe0, 0x0, 0x3, 0xc0, 0x0,
    0x7, 0x80, 0x0, 0xf, 0x0, 0x0, 0x1e, 0x0,
    0x0, 0x3e, 0x0, 0x0, 0xef, 0xff, 0xff, 0x8f,
    0xff, 0xfe, 0x0,

    /* U+F051 "" */
    0xe0, 0x7, 0xe0, 0xf, 0x60, 0x1e, 0x70, 0x3c,
    0x70, 0x78, 0x70, 0xf0, 0x71, 0xe0, 0x3b, 0xc0,
    0x3f, 0x80, 0x3f, 0x0, 0x3e, 0x0, 0x7c, 0x1,
    0xf8, 0x7, 0xf0, 0x19, 0xe0, 0xe3, 0xc3, 0x87,
    0x8e, 0xf, 0x38, 0x1f, 0xc0, 0x3f, 0x0, 0x7c,
    0x0, 0xc0,

    /* U+F060 "" */
    0x0, 0x0, 0x0, 0x0, 0xc, 0x0, 0x0, 0x7,
    0x0, 0x0, 0x3, 0x80, 0x0, 0x1, 0xc0, 0x0,
    0x0, 0xe0, 0x0, 0x0, 0x70, 0x0, 0x0, 0x38,
    0x0, 0x0, 0x1c, 0x0, 0x0, 0xe, 0x0, 0x0,
    0x7, 0x0, 0x0, 0x3, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xdc, 0x0, 0x0, 0x3, 0x80, 0x0,
    0x0, 0x70, 0x0, 0x0, 0xe, 0x0, 0x0, 0x1,
    0xc0, 0x0, 0x0, 0x38, 0x0, 0x0, 0x7, 0x0,
    0x0, 0x0, 0xe0, 0x0, 0x0, 0x1c, 0x0, 0x0,
    0x3, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F061 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x60, 0x0, 0x0,
    0x6, 0x0, 0x0, 0x0, 0x60, 0x0, 0x0, 0x6,
    0x0, 0x0, 0x0, 0x60, 0x0, 0x0, 0x6, 0x0,
    0x0, 0x0, 0x60, 0x0, 0x0, 0x6, 0x0, 0x0,
    0x0, 0x60, 0x0, 0x0, 0x6, 0x7f, 0xff, 0xff,
    0xef, 0xff, 0xff, 0xfc, 0x0, 0x0, 0x3, 0x0,
    0x0, 0x0, 0xc0, 0x0, 0x0, 0x30, 0x0, 0x0,
    0xc, 0x0, 0x0, 0x3, 0x0, 0x0, 0x0, 0xc0,
    0x0, 0x0, 0x30, 0x0, 0x0, 0xc, 0x0, 0x0,
    0x3, 0x0, 0x0, 0x0, 0xc0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F062 "" */
    0x0, 0x0, 0x0, 0x0, 0x18, 0x0, 0x0, 0x3c,
    0x0, 0x0, 0x7e, 0x0, 0x0, 0xdb, 0x0, 0x1,
    0x99, 0x80, 0x3, 0x18, 0xc0, 0x6, 0x18, 0x60,
    0xc, 0x18, 0x30, 0x18, 0x18, 0x18, 0x30, 0x18,
    0xc, 0x60, 0x18, 0x6, 0x40, 0x18, 0x2, 0x0,
    0x18, 0x0, 0x0, 0x18, 0x0, 0x0, 0x18, 0x0,
    0x0, 0x18, 0x0, 0x0, 0x18, 0x0, 0x0, 0x18,
    0x0, 0x0, 0x18, 0x0, 0x0, 0x18, 0x0, 0x0,
    0x18, 0x0, 0x0, 0x18, 0x0, 0x0, 0x18, 0x0,
    0x0, 0x18, 0x0, 0x0, 0x18, 0x0, 0x0, 0x18,
    0x0,

    /* U+F063 "" */
    0x0, 0x18, 0x0, 0x0, 0x18, 0x0, 0x0, 0x18,
    0x0, 0x0, 0x18, 0x0, 0x0, 0x18, 0x0, 0x0,
    0x18, 0x0, 0x0, 0x18, 0x0, 0x0, 0x18, 0x0,
    0x0, 0x18, 0x0, 0x0, 0x18, 0x0, 0x0, 0x18,
    0x0, 0x0, 0x18, 0x0, 0x0, 0x18, 0x0, 0x0,
    0x18, 0x0, 0x60, 0x18, 0x6, 0x70, 0x18, 0xe,
    0x38, 0x18, 0x1c, 0x1c, 0x18, 0x38, 0xe, 0x18,
    0x70, 0x7, 0x18, 0xe0, 0x3, 0x99, 0xc0, 0x1,
    0xdb, 0x80, 0x0, 0xff, 0x0, 0x0, 0x7e, 0x0,
    0x0, 0x3c, 0x0, 0x0, 0x18, 0x0,

    /* U+F071 "" */
    0x0, 0x7, 0x80, 0x0, 0x0, 0x3f, 0x0, 0x0,
    0x0, 0xcc, 0x0, 0x0, 0x6, 0x18, 0x0, 0x0,
    0x38, 0x70, 0x0, 0x0, 0xc0, 0xc0, 0x0, 0x7,
    0x3, 0x80, 0x0, 0x18, 0xc6, 0x0, 0x0, 0xc3,
    0xc, 0x0, 0x7, 0xc, 0x30, 0x0, 0x18, 0x30,
    0x60, 0x0, 0xe0, 0xc1, 0xc0, 0x3, 0x3, 0x3,
    0x0, 0x18, 0xc, 0x6, 0x0, 0xe0, 0x30, 0x18,
    0x3, 0x0, 0xc0, 0x30, 0x1c, 0x0, 0x0, 0xe0,
    0x60, 0x0, 0x1, 0x83, 0x0, 0x0, 0x3, 0x1c,
    0x0, 0xc0, 0xc, 0x60, 0x3, 0x0, 0x1b, 0x80,
    0x0, 0x0, 0x7c, 0x0, 0x0, 0x0, 0xf0, 0x0,
    0x0, 0x3, 0xff, 0xff, 0xff, 0xfd, 0xff, 0xff,
    0xff, 0xe0,

    /* U+F075 "" */
    0x0, 0x1f, 0xe0, 0x0, 0x3, 0xff, 0xf0, 0x0,
    0x3e, 0x1, 0xf0, 0x3, 0xc0, 0x0, 0xf0, 0x1c,
    0x0, 0x0, 0xe0, 0xe0, 0x0, 0x1, 0xc3, 0x0,
    0x0, 0x3, 0x18, 0x0, 0x0, 0x6, 0x60, 0x0,
    0x0, 0x1b, 0x0, 0x0, 0x0, 0x3c, 0x0, 0x0,
    0x0, 0xf0, 0x0, 0x0, 0x3, 0xc0, 0x0, 0x0,
    0xf, 0x0, 0x0, 0x0, 0x3c, 0x0, 0x0, 0x0,
    0xd8, 0x0, 0x0, 0x6, 0x60, 0x0, 0x0, 0x18,
    0xc0, 0x0, 0x0, 0xc3, 0x80, 0x0, 0x7, 0x6,
    0x0, 0x0, 0x38, 0x10, 0x0, 0x3, 0xc0, 0xc3,
    0x80, 0x7c, 0x3, 0x3f, 0xff, 0xc0, 0x1b, 0xc7,
    0xf8, 0x0, 0xfc, 0x0, 0x0, 0x3, 0xc0, 0x0,
    0x0, 0x0,

    /* U+F0AC "" */
    0x0, 0x1f, 0xe0, 0x0, 0x3, 0xff, 0xf0, 0x0,
    0x1f, 0xcf, 0xe0, 0x1, 0xec, 0xd, 0xe0, 0xe,
    0x70, 0x39, 0xc0, 0x71, 0x80, 0x63, 0x81, 0x8c,
    0x0, 0xc6, 0xc, 0x30, 0x3, 0xc, 0x60, 0xc0,
    0xc, 0x19, 0xff, 0xff, 0xff, 0xe7, 0xff, 0xff,
    0xff, 0xb0, 0x60, 0x1, 0x83, 0xc1, 0x80, 0x6,
    0xf, 0x6, 0x0, 0x18, 0x3c, 0x18, 0x0, 0x60,
    0xf0, 0x60, 0x1, 0x83, 0xc1, 0x80, 0x6, 0xf,
    0x6, 0x0, 0x18, 0x3f, 0xff, 0xff, 0xff, 0xdf,
    0xff, 0xff, 0xfe, 0x60, 0xc0, 0xc, 0x19, 0xc3,
    0x0, 0x30, 0xe3, 0x8c, 0x0, 0xc7, 0x6, 0x18,
    0x6, 0x18, 0x1c, 0x60, 0x18, 0xe0, 0x39, 0xc0,
    0xe7, 0x0, 0x7b, 0x7, 0x78, 0x0, 0x7f, 0x3f,
    0x80, 0x0, 0xff, 0xfc, 0x0, 0x0, 0x7f, 0x80,
    0x0,

    /* U+F0F3 "" */
    0x0, 0xc, 0x0, 0x0, 0x3, 0x0, 0x0, 0x3,
    0xf0, 0x0, 0x3, 0xff, 0x80, 0x1, 0xe0, 0xf0,
    0x0, 0xe0, 0xe, 0x0, 0x70, 0x1, 0xc0, 0x18,
    0x0, 0x30, 0xc, 0x0, 0xe, 0x3, 0x0, 0x1,
    0x80, 0xc0, 0x0, 0x60, 0x30, 0x0, 0x18, 0xc,
    0x0, 0x6, 0x3, 0x0, 0x1, 0x80, 0xc0, 0x0,
    0x60, 0x30, 0x0, 0x18, 0x18, 0x0, 0x7, 0x6,
    0x0, 0x0, 0xc3, 0x0, 0x0, 0x39, 0x80, 0x0,
    0x6, 0xe0, 0x0, 0x0, 0xf0, 0x0, 0x0, 0x3f,
    0xff, 0xff, 0xfd, 0xff, 0xff, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x10, 0x0,
    0x0, 0xce, 0x0, 0x0, 0x3f, 0x0, 0x0, 0x7,
    0x80, 0x0,

    /* U+F118 "" */
    0x0, 0x1f, 0xe0, 0x0, 0x3, 0xff, 0xf0, 0x0,
    0x1e, 0x1, 0xe0, 0x1, 0xe0, 0x1, 0xe0, 0xe,
    0x0, 0x1, 0xc0, 0x70, 0x0, 0x3, 0x81, 0x80,
    0x0, 0x6, 0xc, 0x0, 0x0, 0xc, 0x70, 0x0,
    0x0, 0x39, 0x80, 0x0, 0x0, 0x66, 0x0, 0x0,
    0x1, 0xb0, 0x1c, 0xe, 0x3, 0xc0, 0x70, 0x38,
    0xf, 0x0, 0x0, 0x0, 0x3c, 0x0, 0x0, 0x0,
    0xf0, 0x0, 0x0, 0x3, 0xc0, 0x0, 0x0, 0xf,
    0x0, 0x0, 0x0, 0x3c, 0x8, 0x0, 0x40, 0xd8,
    0x38, 0x7, 0x6, 0x60, 0x70, 0x38, 0x19, 0xc0,
    0xff, 0xc0, 0xe3, 0x0, 0xfc, 0x3, 0x6, 0x0,
    0x0, 0x18, 0x1c, 0x0, 0x0, 0xe0, 0x38, 0x0,
    0x7, 0x0, 0x78, 0x0, 0x78, 0x0, 0x78, 0x7,
    0x80, 0x0, 0xff, 0xfc, 0x0, 0x0, 0x7f, 0x80,
    0x0,

    /* U+F124 "" */
    0x0, 0x0, 0xf, 0x0, 0x0, 0x3f, 0x0, 0x1,
    0xf3, 0x0, 0x7, 0xc3, 0x0, 0x3e, 0x6, 0x0,
    0xf8, 0x6, 0x7, 0xc0, 0xc, 0x1f, 0x0, 0xc,
    0x78, 0x0, 0xc, 0xe0, 0x0, 0x18, 0xff, 0xf8,
    0x18, 0xff, 0xfc, 0x30, 0x0, 0xc, 0x30, 0x0,
    0xc, 0x30, 0x0, 0xc, 0x60, 0x0, 0xc, 0x60,
    0x0, 0xc, 0xc0, 0x0, 0xc, 0xc0, 0x0, 0xc,
    0x80, 0x0, 0xd, 0x80, 0x0, 0xd, 0x80, 0x0,
    0xf, 0x0, 0x0, 0xf, 0x0, 0x0, 0xe, 0x0,

    /* U+F1EB "" */
    0x0, 0x3, 0xff, 0x0, 0x0, 0x0, 0xff, 0xff,
    0x80, 0x0, 0xf, 0xc0, 0xf, 0xc0, 0x0, 0xf0,
    0x0, 0x7, 0xc0, 0xf, 0x0, 0x0, 0x3, 0x80,
    0x70, 0x0, 0x0, 0x7, 0x83, 0x80, 0x0, 0x0,
    0x7, 0x38, 0x0, 0x0, 0x0, 0xe, 0xc0, 0x0,
    0x0, 0x0, 0x18, 0x0, 0x7, 0xf8, 0x0, 0x0,
    0x0, 0xff, 0xf8, 0x0, 0x0, 0xf, 0x80, 0x78,
    0x0, 0x0, 0x70, 0x0, 0x78, 0x0, 0x7, 0x0,
    0x0, 0x70, 0x0, 0x18, 0x0, 0x0, 0xe0, 0x0,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7,
    0x80, 0x0, 0x0, 0x0, 0x3f, 0x0, 0x0, 0x0,
    0x1, 0x8c, 0x0, 0x0, 0x0, 0x6, 0x10, 0x0,
    0x0, 0x0, 0x18, 0x40, 0x0, 0x0, 0x0, 0x73,
    0x0, 0x0, 0x0, 0x0, 0xfc, 0x0, 0x0, 0x0,
    0x1, 0xe0, 0x0, 0x0,

    /* U+F1F8 "" */
    0x0, 0x7f, 0x80, 0x0, 0x3f, 0xf0, 0x0, 0x18,
    0x6, 0x3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xcc, 0x0, 0x0, 0xc3, 0x0, 0x0, 0x30, 0xc0,
    0x0, 0xc, 0x30, 0x0, 0x3, 0xc, 0x0, 0x0,
    0xc3, 0x0, 0x0, 0x30, 0xc0, 0x0, 0xc, 0x30,
    0x0, 0x3, 0x4, 0x0, 0x0, 0x81, 0x80, 0x0,
    0x20, 0x60, 0x0, 0x18, 0x18, 0x0, 0x6, 0x6,
    0x0, 0x1, 0x81, 0x80, 0x0, 0x60, 0x60, 0x0,
    0x18, 0x18, 0x0, 0x6, 0x6, 0x0, 0x1, 0x81,
    0x80, 0x0, 0x60, 0x60, 0x0, 0x18, 0x18, 0x0,
    0x6, 0x6, 0x0, 0x1, 0x81, 0x80, 0x0, 0x60,
    0x30, 0x0, 0x30, 0xf, 0xff, 0xfc, 0x1, 0xff,
    0xfe, 0x0,

    /* U+F240 "" */
    0x3f, 0xff, 0xff, 0xf0, 0x1f, 0xff, 0xff, 0xfe,
    0xe, 0x0, 0x0, 0x1, 0xc3, 0x0, 0x0, 0x0,
    0x30, 0xc7, 0xff, 0xff, 0x8c, 0x33, 0xff, 0xff,
    0xf3, 0xc, 0xc0, 0x0, 0xc, 0xcf, 0x30, 0x0,
    0x3, 0x33, 0xcc, 0x0, 0x0, 0xcc, 0xf3, 0x0,
    0x0, 0x33, 0x3c, 0xc0, 0x0, 0xc, 0xcf, 0x30,
    0x0, 0x3, 0x33, 0xcc, 0x0, 0x0, 0xcc, 0xf3,
    0xff, 0xff, 0xf3, 0xc, 0x7f, 0xff, 0xf8, 0xc3,
    0x0, 0x0, 0x0, 0x30, 0xe0, 0x0, 0x0, 0x1c,
    0x1f, 0xff, 0xff, 0xfe, 0x3, 0xff, 0xff, 0xff,
    0x0,

    /* U+F241 "" */
    0x3f, 0xff, 0xff, 0xf0, 0x1f, 0xff, 0xff, 0xfe,
    0xe, 0x0, 0x0, 0x1, 0xc3, 0x0, 0x0, 0x0,
    0x30, 0xc7, 0xff, 0xf0, 0xc, 0x33, 0xff, 0xfe,
    0x3, 0xc, 0xc0, 0x1, 0x80, 0xcf, 0x30, 0x0,
    0x60, 0x33, 0xcc, 0x0, 0x18, 0xc, 0xf3, 0x0,
    0x6, 0x3, 0x3c, 0xc0, 0x1, 0x80, 0xcf, 0x30,
    0x0, 0x60, 0x33, 0xcc, 0x0, 0x18, 0xc, 0xf3,
    0xff, 0xfe, 0x3, 0xc, 0x7f, 0xff, 0x0, 0xc3,
    0x0, 0x0, 0x0, 0x30, 0xe0, 0x0, 0x0, 0x1c,
    0x1f, 0xff, 0xff, 0xfe, 0x3, 0xff, 0xff, 0xff,
    0x0,

    /* U+F242 "" */
    0x3f, 0xff, 0xff, 0xf0, 0x1f, 0xff, 0xff, 0xfe,
    0xe, 0x0, 0x0, 0x1, 0xc3, 0x0, 0x0, 0x0,
    0x30, 0xc7, 0xff, 0x0, 0xc, 0x33, 0xff, 0xe0,
    0x3, 0xc, 0xc0, 0x18, 0x0, 0xcf, 0x30, 0x6,
    0x0, 0x33, 0xcc, 0x1, 0x80, 0xc, 0xf3, 0x0,
    0x60, 0x3, 0x3c, 0xc0, 0x18, 0x0, 0xcf, 0x30,
    0x6, 0x0, 0x33, 0xcc, 0x1, 0x80, 0xc, 0xf3,
    0xff, 0xe0, 0x3, 0xc, 0x7f, 0xf0, 0x0, 0xc3,
    0x0, 0x0, 0x0, 0x30, 0xe0, 0x0, 0x0, 0x1c,
    0x1f, 0xff, 0xff, 0xfe, 0x3, 0xff, 0xff, 0xff,
    0x0,

    /* U+F243 "" */
    0x3f, 0xff, 0xff, 0xf0, 0x1f, 0xff, 0xff, 0xfe,
    0xe, 0x0, 0x0, 0x1, 0xc3, 0x0, 0x0, 0x0,
    0x30, 0xc7, 0xc0, 0x0, 0xc, 0x33, 0xf8, 0x0,
    0x3, 0xc, 0xc6, 0x0, 0x0, 0xcf, 0x31, 0x80,
    0x0, 0x33, 0xcc, 0x60, 0x0, 0xc, 0xf3, 0x18,
    0x0, 0x3, 0x3c, 0xc6, 0x0, 0x0, 0xcf, 0x31,
    0x80, 0x0, 0x33, 0xcc, 0x60, 0x0, 0xc, 0xf3,
    0xf8, 0x0, 0x3, 0xc, 0x7c, 0x0, 0x0, 0xc3,
    0x0, 0x0, 0x0, 0x30, 0xe0, 0x0, 0x0, 0x1c,
    0x1f, 0xff, 0xff, 0xfe, 0x3, 0xff, 0xff, 0xff,
    0x0,

    /* U+F244 "" */
    0x3f, 0xff, 0xff, 0xf0, 0x1f, 0xff, 0xff, 0xfe,
    0xe, 0x0, 0x0, 0x1, 0xc3, 0x0, 0x0, 0x0,
    0x30, 0xc0, 0x0, 0x0, 0xc, 0x30, 0x0, 0x0,
    0x3, 0xc, 0x0, 0x0, 0x0, 0xcf, 0x0, 0x0,
    0x0, 0x33, 0xc0, 0x0, 0x0, 0xc, 0xf0, 0x0,
    0x0, 0x3, 0x3c, 0x0, 0x0, 0x0, 0xcf, 0x0,
    0x0, 0x0, 0x33, 0xc0, 0x0, 0x0, 0xc, 0xf0,
    0x0, 0x0, 0x3, 0xc, 0x0, 0x0, 0x0, 0xc3,
    0x0, 0x0, 0x0, 0x30, 0xe0, 0x0, 0x0, 0x1c,
    0x1f, 0xff, 0xff, 0xfe, 0x3, 0xff, 0xff, 0xff,
    0x0,

    /* U+F293 "" */
    0x0, 0x60, 0x0, 0xe, 0x0, 0x1, 0xe0, 0x0,
    0x37, 0x0, 0x6, 0x70, 0x0, 0xc7, 0x0, 0x18,
    0x72, 0x3, 0x7, 0x70, 0x60, 0x67, 0xc, 0x38,
    0x71, 0x8e, 0x7, 0x33, 0x80, 0x7f, 0xe0, 0x3,
    0xf0, 0x0, 0x3c, 0x0, 0xf, 0xc0, 0x3, 0xfc,
    0x0, 0xed, 0xc0, 0x39, 0x9c, 0x1c, 0x30, 0xe7,
    0x6, 0xe, 0xc0, 0xc0, 0xc0, 0x18, 0x38, 0x3,
    0xc, 0x0, 0x67, 0x0, 0xd, 0xc0, 0x1, 0xf0,
    0x0, 0x3c, 0x0, 0x7, 0x0, 0x0, 0xc0, 0x0,

    /* U+F376 "" */
    0x3f, 0xff, 0x7, 0xf0, 0x1f, 0xff, 0x99, 0xfe,
    0xe, 0x0, 0xf, 0x1, 0xc3, 0x0, 0x7, 0x80,
    0x30, 0xc0, 0x3, 0xe0, 0xc, 0x30, 0x1, 0xf0,
    0x3, 0xc, 0x0, 0xec, 0x0, 0xcf, 0x0, 0x77,
    0xe0, 0x33, 0xc0, 0x39, 0xfc, 0xc, 0xf0, 0x1c,
    0xe, 0x3, 0x3c, 0xf, 0xe7, 0x0, 0xcf, 0x1,
    0xfb, 0x80, 0x33, 0xc0, 0xd, 0xc0, 0xc, 0xf0,
    0x3, 0xe0, 0x3, 0xc, 0x1, 0xf0, 0x0, 0xc3,
    0x0, 0x78, 0x0, 0x30, 0xe0, 0x3c, 0x0, 0x1c,
    0x1f, 0xe6, 0x7f, 0xfe, 0x3, 0xf8, 0x3f, 0xff,
    0x0,

    /* U+F377 "" */
    0xc0, 0x0, 0x0, 0x0, 0x3, 0xc0, 0x0, 0x0,
    0x0, 0x3, 0x80, 0x0, 0x0, 0x0, 0x7, 0x0,
    0x0, 0x0, 0x0, 0xe, 0x0, 0x0, 0x0, 0x0,
    0x1e, 0x1f, 0xff, 0xf0, 0x0, 0x1c, 0x1f, 0xff,
    0xe0, 0x4, 0x38, 0x0, 0x1, 0xc0, 0x30, 0x70,
    0x0, 0x3, 0x0, 0xc0, 0x70, 0x0, 0xc, 0x3,
    0x0, 0xe0, 0x0, 0x30, 0xc, 0x1, 0xc0, 0x0,
    0xcc, 0x30, 0x3, 0x80, 0x3, 0x30, 0xc0, 0x3,
    0x80, 0xc, 0xc3, 0x0, 0x7, 0x0, 0x33, 0xc,
    0x0, 0xe, 0x0, 0xcc, 0x30, 0x0, 0x1e, 0x3,
    0x30, 0xc0, 0x0, 0x1c, 0xc, 0xc3, 0x0, 0x0,
    0x38, 0x30, 0xc, 0x0, 0x0, 0x70, 0x40, 0x30,
    0x0, 0x0, 0xf0, 0x0, 0xe0, 0x0, 0x0, 0xe0,
    0x1, 0xff, 0xff, 0xe1, 0xc0, 0x3, 0xff, 0xff,
    0xc3, 0x80, 0x0, 0x0, 0x0, 0x3, 0x80, 0x0,
    0x0, 0x0, 0x7, 0x0, 0x0, 0x0, 0x0, 0xe,
    0x0, 0x0, 0x0, 0x0, 0x1c, 0x0, 0x0, 0x0,
    0x0, 0x18, 0x0, 0x0, 0x0, 0x0, 0x20,

    /* U+F3C5 "" */
    0x0, 0xfc, 0x0, 0x1f, 0xfe, 0x0, 0xf0, 0x3c,
    0x7, 0x0, 0x38, 0x38, 0x0, 0x71, 0xc0, 0x0,
    0xe6, 0x0, 0x1, 0x98, 0x1e, 0x6, 0xc0, 0xce,
    0xf, 0x6, 0x18, 0x3c, 0x18, 0x70, 0xf0, 0x61,
    0xc3, 0xc1, 0x87, 0xf, 0x7, 0x38, 0x36, 0xf,
    0xe1, 0x98, 0x1f, 0x6, 0x70, 0x0, 0x38, 0xc0,
    0x0, 0xc3, 0x80, 0x7, 0x6, 0x0, 0x18, 0x1c,
    0x0, 0xc0, 0x30, 0x3, 0x0, 0x60, 0x18, 0x1,
    0xc0, 0xe0, 0x3, 0x3, 0x0, 0x6, 0x18, 0x0,
    0x1c, 0xc0, 0x0, 0x3f, 0x0, 0x0, 0x78, 0x0,
    0x0, 0xc0, 0x0,

    /* U+F4DA "" */
    0x0, 0x1f, 0xe0, 0x0, 0x3, 0xff, 0xf0, 0x0,
    0x1e, 0x1, 0xe0, 0x1, 0xe0, 0x1, 0xe0, 0xe,
    0x0, 0x1, 0xc0, 0x70, 0x0, 0x3, 0x81, 0x80,
    0x0, 0x6, 0xc, 0x0, 0x0, 0xc, 0x70, 0x0,
    0x0, 0x39, 0x80, 0x0, 0x0, 0x66, 0x6, 0x3,
    0xc1, 0xb0, 0x1c, 0x3f, 0x83, 0xc0, 0x60, 0xc6,
    0xf, 0x0, 0x0, 0x0, 0x3c, 0x0, 0x0, 0x0,
    0xf0, 0x0, 0x0, 0x3, 0xc0, 0x0, 0x0, 0xf,
    0x0, 0x0, 0x0, 0x3c, 0x8, 0x0, 0x40, 0xd8,
    0x38, 0x7, 0x6, 0x60, 0x70, 0x38, 0x19, 0xc0,
    0xff, 0xc0, 0xe3, 0x0, 0xfc, 0x3, 0x6, 0x0,
    0x0, 0x18, 0x1c, 0x0, 0x0, 0xe0, 0x38, 0x0,
    0x7, 0x0, 0x78, 0x0, 0x78, 0x0, 0x78, 0x7,
    0x80, 0x0, 0xff, 0xfc, 0x0, 0x0, 0x7f, 0x80,
    0x0,

    /* U+F556 "" */
    0x0, 0x1f, 0xe0, 0x0, 0x3, 0xff, 0xf0, 0x0,
    0x1e, 0x1, 0xe0, 0x1, 0xe0, 0x1, 0xe0, 0xe,
    0x0, 0x1, 0xc0, 0x70, 0x0, 0x3, 0x81, 0x80,
    0x0, 0x6, 0xc, 0x0, 0x0, 0xc, 0x70, 0x0,
    0x0, 0x39, 0x80, 0x0, 0x0, 0x66, 0x1c, 0x0,
    0x71, 0xb0, 0x7e, 0xf, 0x83, 0xc0, 0x3c, 0xf8,
    0xf, 0x1, 0xd0, 0xe0, 0x3c, 0x6, 0x1, 0x80,
    0xf0, 0x0, 0x0, 0x3, 0xc0, 0x0, 0x0, 0xf,
    0x0, 0x0, 0x0, 0x3c, 0x0, 0x0, 0x0, 0xd8,
    0x3, 0xf0, 0x6, 0x60, 0x1f, 0xe0, 0x19, 0xc0,
    0xe1, 0xc0, 0xe3, 0x3, 0x3, 0x3, 0x6, 0x0,
    0x0, 0x18, 0x1c, 0x0, 0x0, 0xe0, 0x38, 0x0,
    0x7, 0x0, 0x78, 0x0, 0x78, 0x0, 0x78, 0x7,
    0x80, 0x0, 0xff, 0xfc, 0x0, 0x0, 0x7f, 0x80,
    0x0,

    /* U+F579 "" */
    0x0, 0x1f, 0xe0, 0x0, 0x3, 0xff, 0xf0, 0x0,
    0x1e, 0x1, 0xe0, 0x1, 0xe0, 0x1, 0xe0, 0xe,
    0x0, 0x1, 0xc0, 0x70, 0x0, 0x3, 0x81, 0x80,
    0x0, 0x6, 0xc, 0x0, 0x0, 0xc, 0x71, 0xe0,
    0x1c, 0x39, 0x8f, 0xe0, 0xfc, 0x66, 0x71, 0x86,
    0x31, 0xb1, 0x83, 0x18, 0x63, 0xc6, 0x6c, 0xcd,
    0x8f, 0x19, 0xb3, 0x36, 0x3c, 0x60, 0xc6, 0x18,
    0xf1, 0xc6, 0x1d, 0xc3, 0xc3, 0xf8, 0x3e, 0xf,
    0x7, 0x80, 0x20, 0x3c, 0x0, 0x0, 0x0, 0xd8,
    0x0, 0x0, 0x6, 0x60, 0x0, 0x0, 0x19, 0xc0,
    0x7f, 0xc0, 0xe3, 0x1, 0xff, 0x3, 0x6, 0x0,
    0x0, 0x18, 0x1c, 0x0, 0x0, 0xe0, 0x38, 0x0,
    0x7, 0x0, 0x78, 0x0, 0x78, 0x0, 0x78, 0x7,
    0x80, 0x0, 0xff, 0xfc, 0x0, 0x0, 0x7f, 0x80,
    0x0,

    /* U+F584 "" */
    0x0, 0x1f, 0xe0, 0x0, 0x3, 0xff, 0xf0, 0x0,
    0x1e, 0x1, 0xe0, 0x1, 0xe0, 0x1, 0xe0, 0xe,
    0x0, 0x1, 0xc0, 0x70, 0x0, 0x3, 0x81, 0x83,
    0x3, 0x6, 0xc, 0x1e, 0x1e, 0xc, 0x71, 0xfc,
    0xfe, 0x39, 0x9f, 0x33, 0x3e, 0x66, 0x7c, 0xcc,
    0xf9, 0xb1, 0x83, 0x30, 0x63, 0xc6, 0x18, 0x61,
    0x8f, 0xf, 0xe1, 0xfc, 0x3c, 0xf, 0x87, 0xc0,
    0xf0, 0x0, 0x0, 0x3, 0xc0, 0x0, 0x0, 0xf,
    0x0, 0x0, 0x0, 0x3c, 0x1c, 0x0, 0xe0, 0xd8,
    0x7f, 0xff, 0x86, 0x61, 0xff, 0xfe, 0x19, 0xc3,
    0xc0, 0xf0, 0xe3, 0x3, 0xff, 0x3, 0x6, 0x3,
    0xf0, 0x18, 0x1c, 0x0, 0x0, 0xe0, 0x38, 0x0,
    0x7, 0x0, 0x78, 0x0, 0x78, 0x0, 0x78, 0x7,
    0x80, 0x0, 0xff, 0xfc, 0x0, 0x0, 0x7f, 0x80,
    0x0,

    /* U+F588 "" */
    0x0, 0x1, 0xfe, 0x0, 0x0, 0x0, 0x3f, 0xfe,
    0x0, 0x0, 0x1, 0xe0, 0x3e, 0x0, 0x0, 0x1e,
    0x0, 0x1c, 0x0, 0x0, 0xe0, 0x0, 0x18, 0x0,
    0x7, 0x0, 0x0, 0x30, 0x0, 0x38, 0x0, 0x0,
    0x60, 0x0, 0xc0, 0x0, 0x1, 0xc0, 0x6, 0x0,
    0x0, 0x3, 0x0, 0x18, 0x1c, 0xe, 0x6, 0x0,
    0x41, 0xf8, 0x7c, 0x18, 0x0, 0x6, 0x73, 0x98,
    0x0, 0x0, 0x10, 0xcc, 0x60, 0x0, 0x1c, 0x40,
    0x20, 0x8e, 0x7, 0xf0, 0x0, 0x0, 0x3f, 0x3e,
    0x80, 0x0, 0x0, 0x5f, 0xc6, 0x0, 0x0, 0x1,
    0x8f, 0x18, 0x0, 0x0, 0x6, 0x3e, 0x63, 0x80,
    0xe, 0x19, 0xdf, 0x7, 0xff, 0xf8, 0x3e, 0x38,
    0x1f, 0xff, 0xc0, 0x70, 0x0, 0x3c, 0xe, 0x8,
    0x0, 0x30, 0x7f, 0xf0, 0x70, 0x0, 0xe0, 0x7f,
    0x1, 0x80, 0x1, 0xc0, 0x0, 0xc, 0x0, 0x3,
    0x80, 0x0, 0xe0, 0x0, 0x7, 0x80, 0x7, 0x0,
    0x0, 0x7, 0x80, 0xf8, 0x0, 0x0, 0xf, 0xff,
    0x80, 0x0, 0x0, 0x7, 0xf8, 0x0, 0x0,

    /* U+F598 "" */
    0x0, 0x1f, 0xe0, 0x0, 0x3, 0xff, 0xf0, 0x0,
    0x1e, 0x1, 0xe0, 0x1, 0xe0, 0x1, 0xe0, 0xe,
    0x0, 0x1, 0xc0, 0x70, 0x0, 0x3, 0x81, 0x80,
    0x0, 0x6, 0xc, 0x0, 0x0, 0xc, 0x70, 0x0,
    0x0, 0x39, 0x80, 0x0, 0x0, 0x66, 0x6, 0x3,
    0xc1, 0xb0, 0x1c, 0x3f, 0x83, 0xc0, 0x60, 0xc6,
    0xf, 0x0, 0x0, 0x0, 0x3c, 0x0, 0x0, 0x0,
    0xf0, 0x3, 0x80, 0x3, 0xc0, 0xf, 0x80, 0xf,
    0x0, 0x7, 0x0, 0x3c, 0x0, 0xc, 0x0, 0xd8,
    0x1, 0xe1, 0xe0, 0x60, 0xf, 0xf, 0xc1, 0xc0,
    0xe, 0x33, 0xe3, 0x0, 0xc, 0xcf, 0xc6, 0x0,
    0x63, 0x3, 0x1c, 0xf, 0x8c, 0xc, 0x38, 0x10,
    0x10, 0x70, 0x78, 0x0, 0x6f, 0x80, 0x78, 0x9,
    0xf8, 0x0, 0xff, 0xe2, 0x0, 0x0, 0x7f, 0x80,
    0x0,

    /* U+F59B "" */
    0x0, 0x1f, 0xe0, 0x0, 0x3, 0xff, 0xf0, 0x0,
    0x1e, 0x1, 0xe0, 0x1, 0xe0, 0x1, 0xe0, 0xe,
    0x0, 0x1, 0xc0, 0x70, 0x0, 0x3, 0x81, 0x80,
    0x0, 0x6, 0xc, 0x0, 0x0, 0xc, 0x70, 0x0,
    0x0, 0x39, 0x83, 0x80, 0x70, 0x66, 0xf, 0x7,
    0xc1, 0xb0, 0xe, 0x1c, 0x3, 0xc0, 0xf0, 0x3c,
    0xf, 0x3, 0x0, 0x30, 0x3c, 0x0, 0x0, 0x0,
    0xf0, 0x0, 0x0, 0x3, 0xc0, 0x0, 0x0, 0xf,
    0xf, 0xff, 0xfc, 0x3c, 0x3f, 0xff, 0xf0, 0xd8,
    0xe0, 0x1, 0x86, 0x61, 0x80, 0x6, 0x19, 0xc3,
    0x0, 0x30, 0xe3, 0x7, 0x3, 0x83, 0x6, 0xf,
    0xfc, 0x18, 0x1c, 0xf, 0xc0, 0xe0, 0x38, 0x0,
    0x7, 0x0, 0x78, 0x0, 0x78, 0x0, 0x78, 0x7,
    0x80, 0x0, 0xff, 0xfc, 0x0, 0x0, 0x7f, 0x80,
    0x0,

    /* U+F5A4 "" */
    0x0, 0x1f, 0xe0, 0x0, 0x3, 0xff, 0xf0, 0x0,
    0x1e, 0x1, 0xe0, 0x1, 0xe0, 0x1, 0xe0, 0xe,
    0x0, 0x1, 0xc0, 0x70, 0x0, 0x3, 0x81, 0x80,
    0x0, 0x6, 0xc, 0x0, 0x0, 0xc, 0x70, 0x0,
    0x0, 0x39, 0x80, 0x0, 0x0, 0x66, 0x0, 0x0,
    0x1, 0xb0, 0x1c, 0xe, 0x3, 0xc0, 0x70, 0x38,
    0xf, 0x0, 0x80, 0x40, 0x3c, 0x0, 0x0, 0x0,
    0xf0, 0x0, 0x0, 0x3, 0xc0, 0x0, 0x0, 0xf,
    0x0, 0x0, 0x0, 0x3c, 0x0, 0x0, 0x0, 0xd8,
    0x0, 0x0, 0x6, 0x60, 0x0, 0x0, 0x19, 0xc0,
    0x0, 0x0, 0xe3, 0x0, 0x0, 0x3, 0x6, 0x0,
    0x0, 0x18, 0x1c, 0x0, 0x0, 0xe0, 0x38, 0x0,
    0x7, 0x0, 0x78, 0x0, 0x78, 0x0, 0x78, 0x7,
    0x80, 0x0, 0xff, 0xfc, 0x0, 0x0, 0x7f, 0x80,
    0x0,

    /* U+F5B3 "" */
    0x0, 0x1f, 0xe0, 0x0, 0x3, 0xff, 0xf0, 0x0,
    0x1e, 0x1, 0xe0, 0x1, 0xe0, 0x1, 0xe0, 0xe,
    0x0, 0x1, 0xc0, 0x70, 0x0, 0x3, 0x81, 0x80,
    0x0, 0x6, 0xc, 0x0, 0x0, 0xc, 0x70, 0x0,
    0x0, 0x39, 0x80, 0x0, 0x0, 0x66, 0x1e, 0x1,
    0xe1, 0xb0, 0xfe, 0x1f, 0xc3, 0xc3, 0x18, 0x63,
    0xf, 0x0, 0x0, 0x0, 0x3c, 0x0, 0x0, 0x0,
    0xf0, 0x61, 0xe1, 0x83, 0xc1, 0x8f, 0xc6, 0xf,
    0x6, 0x73, 0x98, 0x3c, 0x19, 0x86, 0x60, 0xd8,
    0x66, 0x19, 0x86, 0x61, 0x98, 0x66, 0x19, 0xc6,
    0x73, 0x98, 0xe3, 0x18, 0xfc, 0x63, 0x6, 0x61,
    0xe1, 0x98, 0x1d, 0x80, 0x6, 0xe0, 0x3e, 0x0,
    0x1f, 0x0, 0x78, 0x0, 0x78, 0x0, 0x78, 0x7,
    0x80, 0x0, 0xff, 0xfc, 0x0, 0x0, 0x7f, 0x80,
    0x0,

    /* U+F68C "" */
    0xff, 0xfc,

    /* U+F68D "" */
    0x1, 0x80, 0xc0, 0x60, 0x30, 0x18, 0xf, 0x7,
    0x83, 0xc1, 0xe0, 0xf0, 0x78, 0x3c, 0x18,

    /* U+F68E "" */
    0x0, 0x3, 0x0, 0x3, 0x0, 0x3, 0x0, 0x3,
    0x0, 0x3, 0x0, 0x3, 0x1, 0x83, 0x1, 0x83,
    0x1, 0x83, 0x1, 0x83, 0x1, 0x83, 0xc1, 0x83,
    0xc1, 0x83, 0xc1, 0x83, 0xc1, 0x83, 0xc1, 0x83,
    0xc1, 0x83, 0xc1, 0x83,

    /* U+F68F "" */
    0x0, 0x0, 0x3, 0x0, 0x0, 0x3, 0x0, 0x0,
    0x3, 0x0, 0x0, 0x3, 0x0, 0x0, 0x3, 0x0,
    0x0, 0x3, 0x0, 0x1, 0x83, 0x0, 0x1, 0x83,
    0x0, 0x1, 0x83, 0x0, 0x1, 0x83, 0x0, 0x1,
    0x83, 0x1, 0x81, 0x83, 0x1, 0x81, 0x83, 0x1,
    0x81, 0x83, 0x1, 0x81, 0x83, 0x1, 0x81, 0x83,
    0x1, 0x81, 0x83, 0xc1, 0x81, 0x83, 0xc1, 0x81,
    0x83, 0xc1, 0x81, 0x83, 0xc1, 0x81, 0x83, 0xc1,
    0x81, 0x83, 0xc1, 0x81, 0x83, 0xc1, 0x81, 0x83,

    /* U+F695 "" */
    0xc0, 0x0, 0x0, 0x0, 0x3, 0xc0, 0x0, 0x0,
    0x1, 0x83, 0x80, 0x0, 0x0, 0x6, 0x7, 0x0,
    0x0, 0x0, 0x18, 0xe, 0x0, 0x0, 0x0, 0x60,
    0x1e, 0x0, 0x0, 0x1, 0x80, 0x1c, 0x0, 0x6,
    0x6, 0x0, 0x38, 0x0, 0x18, 0x18, 0x0, 0x70,
    0x0, 0x60, 0x60, 0x0, 0x70, 0x1, 0x81, 0x80,
    0x0, 0xe0, 0x6, 0x6, 0x0, 0x1, 0xc0, 0x18,
    0x18, 0x0, 0x3, 0x80, 0x60, 0x60, 0x0, 0x3,
    0x81, 0x81, 0x80, 0x0, 0x7, 0x6, 0x6, 0x0,
    0x0, 0xe, 0x18, 0x18, 0x0, 0x0, 0x1e, 0x20,
    0x60, 0x0, 0xc0, 0x1c, 0x1, 0x80, 0x3, 0x0,
    0x38, 0x6, 0x0, 0xc, 0x10, 0x70, 0x18, 0x0,
    0x30, 0x30, 0xf0, 0x60, 0x0, 0xc0, 0xc0, 0xe1,
    0x80, 0x3, 0x3, 0x1, 0xc2, 0x6, 0xc, 0xc,
    0x3, 0x80, 0x18, 0x30, 0x30, 0x3, 0x80, 0x60,
    0xc0, 0xc1, 0x7, 0x1, 0x83, 0x3, 0x6, 0xe,
    0x6, 0xc, 0xc, 0x18, 0x1c, 0x18, 0x30, 0x30,
    0x60, 0x18, 0x60, 0xc0, 0xc1, 0x80, 0x20,

    /* U+F6A8 "" */
    0x0, 0x0, 0xc0, 0x0, 0x0, 0x1, 0xe0, 0x0,
    0x0, 0x3, 0x60, 0x0, 0x0, 0x6, 0x60, 0x0,
    0x0, 0x1c, 0x60, 0x30, 0x0, 0x38, 0x60, 0x18,
    0x0, 0x70, 0x60, 0xc, 0x1f, 0xe0, 0x60, 0xe,
    0x7f, 0xc0, 0x63, 0x6, 0xe0, 0x0, 0x63, 0x86,
    0xc0, 0x0, 0x61, 0xc3, 0xc0, 0x0, 0x60, 0xc3,
    0xc0, 0x0, 0x60, 0xc3, 0xc0, 0x0, 0x60, 0xc3,
    0xc0, 0x0, 0x60, 0xc3, 0xc0, 0x0, 0x61, 0x83,
    0xe0, 0x0, 0x63, 0x86, 0x7f, 0xc0, 0x60, 0x6,
    0x1f, 0xe0, 0x60, 0xc, 0x0, 0x70, 0x60, 0x1c,
    0x0, 0x38, 0x60, 0x38, 0x0, 0x1c, 0x60, 0x10,
    0x0, 0xe, 0x60, 0x0, 0x0, 0x3, 0x60, 0x0,
    0x0, 0x1, 0xe0, 0x0, 0x0, 0x0, 0xc0, 0x0,

    /* U+F6A9 "" */
    0x0, 0x0, 0xc0, 0x0, 0x0, 0x0, 0x78, 0x0,
    0x0, 0x0, 0x36, 0x0, 0x0, 0x0, 0x19, 0x80,
    0x0, 0x0, 0x1c, 0x60, 0x0, 0x0, 0xe, 0x18,
    0x0, 0x0, 0x7, 0x6, 0x0, 0x0, 0x7f, 0x81,
    0x84, 0x0, 0x7f, 0xc0, 0x61, 0x80, 0xf8, 0x0,
    0x18, 0x30, 0x6c, 0x0, 0x6, 0x6, 0x33, 0x0,
    0x1, 0x80, 0xd8, 0xc0, 0x0, 0x60, 0x1c, 0x30,
    0x0, 0x18, 0x7, 0xc, 0x0, 0x6, 0x3, 0x63,
    0x0, 0x1, 0x81, 0x8c, 0xe0, 0x0, 0x60, 0xc1,
    0x9f, 0xf0, 0x18, 0x60, 0x33, 0xfe, 0x6, 0x10,
    0x0, 0x1, 0xc1, 0x80, 0x0, 0x0, 0x38, 0x60,
    0x0, 0x0, 0x7, 0x18, 0x0, 0x0, 0x0, 0xe6,
    0x0, 0x0, 0x0, 0xd, 0x80, 0x0, 0x0, 0x1,
    0xe0, 0x0, 0x0, 0x0, 0x30, 0x0, 0x0,

    /* U+F6AA "" */
    0x1e, 0x1f, 0x98, 0xcc, 0x26, 0x13, 0x98, 0xfc,
    0x3c,

    /* U+F6AB "" */
    0x0, 0x7f, 0x80, 0x1, 0xff, 0xf0, 0x3, 0xe0,
    0x1e, 0x3, 0x80, 0x3, 0xc7, 0x0, 0x0, 0x73,
    0x0, 0x0, 0x1d, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0x0, 0x0,
    0xf, 0xc0, 0x0, 0xe, 0x70, 0x0, 0x6, 0x18,
    0x0, 0x3, 0xc, 0x0, 0x1, 0x8e, 0x0, 0x0,
    0x7e, 0x0, 0x0, 0x1e, 0x0, 0x0,

    /* U+F6AC "" */
    0xc0, 0x0, 0x0, 0x0, 0x3, 0xc0, 0x0, 0x0,
    0x0, 0x3, 0x80, 0x3f, 0xf0, 0x0, 0x7, 0xf,
    0xff, 0xf8, 0x0, 0xe, 0x1c, 0x0, 0xfc, 0x0,
    0x1e, 0x0, 0x0, 0x7c, 0x0, 0x1c, 0x0, 0x0,
    0x38, 0x8, 0x38, 0x0, 0x0, 0x78, 0x70, 0x70,
    0x0, 0x0, 0x73, 0x80, 0x70, 0x0, 0x0, 0xec,
    0x0, 0xe0, 0x0, 0x1, 0x80, 0x1, 0xc3, 0x0,
    0x0, 0x0, 0x3, 0x83, 0x80, 0x0, 0x0, 0x83,
    0x87, 0x80, 0x0, 0x7, 0x7, 0x7, 0x80, 0x0,
    0x70, 0xe, 0x7, 0x0, 0x1, 0x80, 0x1e, 0xe,
    0x0, 0x4, 0x0, 0x1c, 0x0, 0x0, 0x0, 0x0,
    0x38, 0x0, 0x0, 0x0, 0x0, 0x70, 0x0, 0x0,
    0x0, 0x70, 0xf0, 0x0, 0x0, 0x3, 0xf0, 0xe0,
    0x0, 0x0, 0x1c, 0xc1, 0xc0, 0x0, 0x0, 0x61,
    0x3, 0x80, 0x0, 0x1, 0x86, 0x3, 0x80, 0x0,
    0x6, 0x30, 0x7, 0x0, 0x0, 0xf, 0xc0, 0xe,
    0x0, 0x0, 0x1e, 0x0, 0x1c, 0x0, 0x0, 0x0,
    0x0, 0x18, 0x0, 0x0, 0x0, 0x0, 0x20,

    /* U+F7C2 "" */
    0x1, 0xff, 0xf8, 0x7, 0xff, 0xf8, 0x1c, 0x0,
    0x38, 0x70, 0x0, 0x31, 0xc6, 0x66, 0x67, 0xc,
    0xcc, 0xdc, 0x19, 0x99, 0xf0, 0x33, 0x33, 0xc0,
    0x66, 0x67, 0x80, 0x0, 0xf, 0x0, 0x0, 0x1e,
    0x0, 0x0, 0x3c, 0x0, 0x0, 0x78, 0x0, 0x0,
    0xf0, 0x0, 0x1, 0xe0, 0x0, 0x3, 0xc0, 0x0,
    0x7, 0x80, 0x0, 0xf, 0x0, 0x0, 0x1e, 0x0,
    0x0, 0x3c, 0x0, 0x0, 0x78, 0x0, 0x0, 0xf0,
    0x0, 0x1, 0xe0, 0x0, 0x3, 0xc0, 0x0, 0x7,
    0x80, 0x0, 0xf, 0x0, 0x0, 0x1f, 0x0, 0x0,
    0x77, 0xff, 0xff, 0xc7, 0xff, 0xff, 0x0
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 420, .box_w = 26, .box_h = 30, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 98, .adv_w = 480, .box_w = 31, .box_h = 31, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 219, .adv_w = 480, .box_w = 30, .box_h = 30, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 332, .adv_w = 480, .box_w = 30, .box_h = 30, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 445, .adv_w = 480, .box_w = 30, .box_h = 30, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 558, .adv_w = 480, .box_w = 30, .box_h = 30, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 671, .adv_w = 480, .box_w = 30, .box_h = 30, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 784, .adv_w = 480, .box_w = 30, .box_h = 30, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 897, .adv_w = 480, .box_w = 30, .box_h = 30, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 1010, .adv_w = 480, .box_w = 30, .box_h = 30, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 1123, .adv_w = 480, .box_w = 30, .box_h = 30, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 1236, .adv_w = 480, .box_w = 30, .box_h = 30, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 1349, .adv_w = 480, .box_w = 30, .box_h = 30, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 1462, .adv_w = 480, .box_w = 30, .box_h = 30, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 1575, .adv_w = 420, .box_w = 26, .box_h = 30, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 1673, .adv_w = 420, .box_w = 27, .box_h = 18, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 1734, .adv_w = 360, .box_w = 19, .box_h = 19, .ofs_x = 2, .ofs_y = 2},
    {.bitmap_index = 1780, .adv_w = 480, .box_w = 28, .box_h = 28, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 1878, .adv_w = 600, .box_w = 32, .box_h = 30, .ofs_x = 3, .ofs_y = -4},
    {.bitmap_index = 1998, .adv_w = 480, .box_w = 28, .box_h = 30, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 2103, .adv_w = 540, .box_w = 34, .box_h = 30, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 2231, .adv_w = 480, .box_w = 30, .box_h = 30, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 2344, .adv_w = 420, .box_w = 26, .box_h = 26, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2429, .adv_w = 600, .box_w = 38, .box_h = 27, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2558, .adv_w = 480, .box_w = 30, .box_h = 26, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2656, .adv_w = 480, .box_w = 30, .box_h = 30, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 2769, .adv_w = 300, .box_w = 15, .box_h = 22, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 2811, .adv_w = 360, .box_w = 23, .box_h = 27, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2889, .adv_w = 300, .box_w = 19, .box_h = 22, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2942, .adv_w = 360, .box_w = 23, .box_h = 23, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3009, .adv_w = 300, .box_w = 15, .box_h = 22, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 3051, .adv_w = 420, .box_w = 26, .box_h = 24, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3129, .adv_w = 420, .box_w = 27, .box_h = 24, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3210, .adv_w = 360, .box_w = 24, .box_h = 27, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 3291, .adv_w = 360, .box_w = 24, .box_h = 26, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 3369, .adv_w = 480, .box_w = 30, .box_h = 26, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3467, .adv_w = 480, .box_w = 30, .box_h = 26, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3565, .adv_w = 480, .box_w = 30, .box_h = 30, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 3678, .adv_w = 420, .box_w = 26, .box_h = 30, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 3776, .adv_w = 480, .box_w = 30, .box_h = 30, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 3889, .adv_w = 420, .box_w = 24, .box_h = 24, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 3961, .adv_w = 600, .box_w = 38, .box_h = 26, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4085, .adv_w = 420, .box_w = 26, .box_h = 30, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 4183, .adv_w = 540, .box_w = 34, .box_h = 19, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 4264, .adv_w = 540, .box_w = 34, .box_h = 19, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 4345, .adv_w = 540, .box_w = 34, .box_h = 19, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 4426, .adv_w = 540, .box_w = 34, .box_h = 19, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 4507, .adv_w = 540, .box_w = 34, .box_h = 19, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 4588, .adv_w = 360, .box_w = 19, .box_h = 30, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 4660, .adv_w = 540, .box_w = 34, .box_h = 19, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 4741, .adv_w = 600, .box_w = 38, .box_h = 30, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 4884, .adv_w = 360, .box_w = 22, .box_h = 30, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 4967, .adv_w = 480, .box_w = 30, .box_h = 30, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 5080, .adv_w = 480, .box_w = 30, .box_h = 30, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 5193, .adv_w = 480, .box_w = 30, .box_h = 30, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 5306, .adv_w = 480, .box_w = 30, .box_h = 30, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 5419, .adv_w = 600, .box_w = 38, .box_h = 30, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 5562, .adv_w = 480, .box_w = 30, .box_h = 30, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 5675, .adv_w = 480, .box_w = 30, .box_h = 30, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 5788, .adv_w = 480, .box_w = 30, .box_h = 30, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 5901, .adv_w = 480, .box_w = 30, .box_h = 30, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 6014, .adv_w = 600, .box_w = 2, .box_h = 7, .ofs_x = 3, .ofs_y = -4},
    {.bitmap_index = 6016, .adv_w = 600, .box_w = 9, .box_h = 13, .ofs_x = 3, .ofs_y = -4},
    {.bitmap_index = 6031, .adv_w = 600, .box_w = 16, .box_h = 18, .ofs_x = 3, .ofs_y = -3},
    {.bitmap_index = 6067, .adv_w = 600, .box_w = 24, .box_h = 24, .ofs_x = 3, .ofs_y = -4},
    {.bitmap_index = 6139, .adv_w = 600, .box_w = 38, .box_h = 30, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 6282, .adv_w = 540, .box_w = 32, .box_h = 26, .ofs_x = 2, .ofs_y = -2},
    {.bitmap_index = 6386, .adv_w = 540, .box_w = 34, .box_h = 26, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6497, .adv_w = 600, .box_w = 9, .box_h = 8, .ofs_x = 14, .ofs_y = -2},
    {.bitmap_index = 6506, .adv_w = 600, .box_w = 25, .box_h = 17, .ofs_x = 6, .ofs_y = -2},
    {.bitmap_index = 6560, .adv_w = 600, .box_w = 38, .box_h = 30, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 6703, .adv_w = 360, .box_w = 23, .box_h = 30, .ofs_x = 0, .ofs_y = -4}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_0[] = {
    0x0, 0x1a1, 0x320, 0x322, 0x327, 0x32a, 0x339, 0x342,
    0x347, 0x34d, 0x350, 0x359, 0x3be, 0xfb6, 0xfbc, 0xfc1,
    0xfc2, 0xfc6, 0xfc7, 0xfc8, 0xfca, 0xfce, 0xfdc, 0xfdd,
    0xff3, 0xff9, 0xffd, 0x1000, 0x1001, 0x1002, 0x1006, 0x1015,
    0x1016, 0x1017, 0x1018, 0x1026, 0x102a, 0x1061, 0x10a8, 0x10cd,
    0x10d9, 0x11a0, 0x11ad, 0x11f5, 0x11f6, 0x11f7, 0x11f8, 0x11f9,
    0x1248, 0x132b, 0x132c, 0x137a, 0x148f, 0x150b, 0x152e, 0x1539,
    0x153d, 0x154d, 0x1550, 0x1559, 0x1568, 0x1641, 0x1642, 0x1643,
    0x1644, 0x164a, 0x165d, 0x165e, 0x165f, 0x1660, 0x1661, 0x1777
};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] =
{
    {
        .range_start = 57419, .range_length = 6008, .glyph_id_start = 1,
        .unicode_list = unicode_list_0, .glyph_id_ofs_list = NULL, .list_length = 72, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    }
};



/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LVGL_VERSION_MAJOR == 8
/*Store all the custom data of the font*/
static  lv_font_fmt_txt_glyph_cache_t cache;
#endif

#if LVGL_VERSION_MAJOR >= 8
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = NULL,
    .kern_scale = 0,
    .cmap_num = 1,
    .bpp = 1,
    .kern_classes = 0,
    .bitmap_format = 0,
#if LVGL_VERSION_MAJOR == 8
    .cache = &cache
#endif
};



/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LVGL_VERSION_MAJOR >= 8
const lv_font_t font_awesome_30_1 = {
#else
lv_font_t font_awesome_30_1 = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 31,          /*The maximum line height required by the font*/
    .base_line = 4,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -2,
    .underline_thickness = 1,
#endif
    .dsc = &font_dsc,          /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
#if LV_VERSION_CHECK(8, 2, 0) || LVGL_VERSION_MAJOR >= 9
    .fallback = NULL,
#endif
    .user_data = NULL,
};



#endif /*#if FONT_AWESOME_30_1*/

