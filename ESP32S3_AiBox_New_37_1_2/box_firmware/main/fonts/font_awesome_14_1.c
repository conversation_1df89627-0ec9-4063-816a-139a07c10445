/*******************************************************************************
 * Size: 14 px
 * Bpp: 1
 * Opts: --no-compress --no-prefilter --force-fast-kern-format --font fa-regular-400.ttf --format lvgl --lv-include lvgl.h --bpp 1 -o font_awesome_14_1.c --size 14 -r 0xf5a4,0xf118,0xf59b,0xf588,0xe384,0xf556,0xf5b3,0xf584,0xf579,0xe36b,0xe375,0xe39b,0xf4da,0xe398,0xe392,0xe372,0xf598,0xe409,0xe38d,0xe3a4,0xe36d,0xf240,0xf241,0xf242,0xf243,0xf244,0xf377,0xf376,0xf1eb,0xf6ab,0xf6aa,0xf6ac,0xf012,0xf68f,0xf68e,0xf68d,0xf68c,0xf695,0xf028,0xf6a8,0xf027,0xf6a9,0xf001,0xf00c,0xf00d,0xf011,0xf013,0xf1f8,0xf015,0xf03e,0xf044,0xf048,0xf051,0xf04b,0xf04c,0xf04d,0xf060,0xf061,0xf062,0xf063,0xf071,0xf0f3,0xf3c5,0xf0ac,0xf124,0xf7c2,0xf293,0xf075,0xe1ec,0xf007,0xe04b,0xf019
 ******************************************************************************/

#ifdef LV_LVGL_H_INCLUDE_SIMPLE
#include "lvgl.h"
#else
#include "lvgl.h"
#endif

#ifndef FONT_AWESOME_14_1
#define FONT_AWESOME_14_1 1
#endif

#if FONT_AWESOME_14_1

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+E04B "" */
    0x2, 0x0, 0x20, 0x1f, 0x8a, 0x5, 0xad, 0x5a,
    0x5, 0xaa, 0x53, 0xf8, 0x0, 0x0, 0x0, 0x7f,
    0xec, 0x3, 0x8b, 0x1f, 0xff,

    /* U+E1EC "" */
    0x9, 0x40, 0x25, 0x3, 0xff, 0x8, 0x4, 0xe0,
    0x1c, 0x89, 0x4e, 0x55, 0xcb, 0xd4, 0x68, 0x5b,
    0x80, 0x72, 0x1, 0xf, 0xfc, 0x9, 0x40, 0x25,
    0x0,

    /* U+E36B "" */
    0xf, 0xc0, 0x61, 0x82, 0x1, 0x16, 0x32, 0xe0,
    0x2e, 0x23, 0x18, 0x8c, 0x60, 0x1, 0x87, 0x7,
    0x1c, 0x34, 0x70, 0x88, 0x4, 0x18, 0x60, 0x3f,
    0x0,

    /* U+E36D "" */
    0xf, 0xc0, 0x61, 0x82, 0x1, 0x10, 0x2, 0xc8,
    0xce, 0x23, 0x18, 0x0, 0x60, 0x1, 0x83, 0xc7,
    0x30, 0x34, 0x80, 0x88, 0x4, 0x18, 0x60, 0x3f,
    0x0,

    /* U+E372 "" */
    0xf, 0xc0, 0x61, 0x82, 0x1, 0x10, 0x2, 0x8c,
    0xc6, 0x40, 0x98, 0x0, 0x60, 0x1, 0x80, 0x26,
    0x21, 0xb4, 0x7e, 0x88, 0x2a, 0x18, 0xa0, 0x38,
    0x80,

    /* U+E375 "" */
    0xf, 0xc0, 0xe1, 0xc7, 0x8f, 0x98, 0x6, 0xfc,
    0xee, 0x95, 0x5b, 0xd5, 0x66, 0x39, 0x80, 0x6,
    0x1c, 0x14, 0xf8, 0x88, 0x4, 0x10, 0x20, 0x3f,
    0x0,

    /* U+E384 "" */
    0xf, 0xc0, 0x61, 0x82, 0x1, 0x12, 0x12, 0xd8,
    0x6e, 0x80, 0x58, 0x0, 0x60, 0x1, 0x9c, 0xe7,
    0x0, 0x34, 0x78, 0x88, 0x4, 0x18, 0x60, 0x3f,
    0x0,

    /* U+E38D "" */
    0x7, 0x9c, 0x7f, 0x33, 0x1, 0xd8, 0x38, 0xc0,
    0x42, 0x3, 0xb8, 0x0, 0x60, 0x1, 0x9c, 0xe6,
    0x0, 0x1c, 0x30, 0x50, 0xc2, 0x20, 0x18, 0x60,
    0xc0, 0xfc, 0x0,

    /* U+E392 "" */
    0xf, 0xc0, 0x61, 0x82, 0x1, 0x10, 0x2, 0xc0,
    0xe, 0x73, 0x98, 0x0, 0x60, 0x1, 0x80, 0x7,
    0x21, 0x34, 0x78, 0x88, 0x4, 0x18, 0x60, 0x3f,
    0x0,

    /* U+E398 "" */
    0xf, 0xc0, 0x40, 0x82, 0x1, 0x1f, 0xfe, 0xff,
    0xff, 0xf3, 0xfb, 0xcf, 0x60, 0x1, 0x80, 0x7,
    0x21, 0x34, 0x78, 0x88, 0x4, 0x18, 0x60, 0x3f,
    0x0,

    /* U+E39B "" */
    0xf, 0xc0, 0x41, 0x82, 0x81, 0x15, 0x82, 0x88,
    0xce, 0x3, 0x18, 0x0, 0x63, 0x1, 0x82, 0x7,
    0x84, 0x36, 0xf0, 0x9f, 0x4, 0x7c, 0x60, 0xee,
    0x0,

    /* U+E3A4 "" */
    0xf, 0xc0, 0xe1, 0x87, 0x1d, 0x18, 0x76, 0xd9,
    0x4f, 0xd2, 0x3f, 0xc7, 0x76, 0x3d, 0xc3, 0xef,
    0x15, 0xa6, 0xc5, 0x8d, 0x1c, 0x1c, 0x60, 0x3f,
    0x0,

    /* U+E409 "" */
    0xf, 0xc0, 0x61, 0x82, 0x1, 0x16, 0x32, 0xed,
    0x6e, 0x94, 0x9b, 0xde, 0x60, 0x1, 0x9f, 0xe7,
    0x7c, 0xb4, 0xe4, 0x89, 0xe4, 0x18, 0x60, 0x3f,
    0x0,

    /* U+F001 "" */
    0x0, 0xc, 0x3, 0xf0, 0x7c, 0x43, 0x1, 0x8,
    0x3c, 0x2f, 0x90, 0xf0, 0x42, 0x1, 0x8, 0x3c,
    0x21, 0x1f, 0x84, 0x62, 0xe, 0x88, 0x1, 0xc0,
    0x0,

    /* U+F007 "" */
    0xf, 0x1, 0x98, 0x10, 0x81, 0x8, 0x10, 0x81,
    0xf8, 0xf, 0x0, 0x0, 0x1f, 0x86, 0x6, 0x40,
    0x2c, 0x3, 0x80, 0x1f, 0xff,

    /* U+F00C "" */
    0x0, 0x10, 0x3, 0x0, 0x60, 0xc, 0xc1, 0x86,
    0x30, 0x36, 0x1, 0xc0, 0x8, 0x0,

    /* U+F00D "" */
    0x81, 0xe1, 0x99, 0x87, 0x81, 0x81, 0xe1, 0x99,
    0x86, 0x81, 0x80,

    /* U+F011 "" */
    0x2, 0x1, 0x11, 0x18, 0x8c, 0x84, 0x2c, 0x21,
    0xc1, 0x6, 0x8, 0x30, 0x1, 0x80, 0xa, 0x0,
    0x98, 0xc, 0x60, 0xc0, 0xf8, 0x0,

    /* U+F012 "" */
    0x0, 0x2, 0x0, 0x4, 0x0, 0x48, 0x0, 0x90,
    0x1, 0x20, 0x22, 0x40, 0x44, 0x80, 0x89, 0x9,
    0x12, 0x12, 0x26, 0x24, 0x4c, 0x48, 0x98, 0x91,
    0x31, 0x22, 0x40,

    /* U+F013 "" */
    0xf, 0x80, 0x44, 0x1e, 0x3c, 0xa0, 0xac, 0x21,
    0xe6, 0xcd, 0x22, 0x49, 0x32, 0xcf, 0x9e, 0x10,
    0xd4, 0x14, 0xf1, 0xe0, 0x88, 0x3, 0x80,

    /* U+F015 "" */
    0x0, 0x0, 0x1, 0x80, 0x7, 0xc0, 0xc, 0x30,
    0x18, 0x18, 0x30, 0xc, 0x60, 0x6, 0xe0, 0x7,
    0x23, 0xc4, 0x22, 0x44, 0x22, 0x44, 0x22, 0x44,
    0x22, 0x44, 0x22, 0x44, 0x1f, 0xf8,

    /* U+F019 "" */
    0x2, 0x0, 0x10, 0x0, 0x80, 0x4, 0x0, 0x20,
    0x19, 0x30, 0x6b, 0x1, 0xf0, 0xf7, 0x7c, 0x10,
    0x60, 0xb, 0x0, 0x58, 0x0, 0xff, 0xfc,

    /* U+F027 "" */
    0x3, 0x0, 0x70, 0xd, 0x7, 0x90, 0xf1, 0x38,
    0x11, 0x81, 0x1f, 0x13, 0x79, 0x0, 0xd0, 0x7,
    0x0, 0x30,

    /* U+F028 "" */
    0x0, 0x0, 0x1, 0x83, 0x1, 0xc0, 0xc1, 0xa1,
    0xa7, 0x90, 0x6f, 0x89, 0x96, 0x4, 0x4b, 0x2,
    0x25, 0xf1, 0x32, 0xbc, 0x82, 0xc3, 0x43, 0x40,
    0xe0, 0x60, 0x30, 0x60,

    /* U+F03E "" */
    0xff, 0xfe, 0x0, 0x18, 0x0, 0x66, 0x1, 0x98,
    0x6, 0x2, 0x18, 0x1c, 0x66, 0xf9, 0xbf, 0xe6,
    0xff, 0xdf, 0xff, 0xff, 0xff,

    /* U+F044 "" */
    0x0, 0x10, 0x1, 0xe7, 0xcc, 0xe0, 0xea, 0x85,
    0x1a, 0x8, 0xc8, 0x46, 0x22, 0x30, 0x89, 0x92,
    0x3c, 0x48, 0x81, 0x20, 0x4, 0x80, 0x11, 0xff,
    0x80,

    /* U+F048 "" */
    0x87, 0x1e, 0xef, 0x1c, 0x38, 0x78, 0xdd, 0x8f,
    0xc,

    /* U+F04B "" */
    0x0, 0x38, 0xb, 0x82, 0x30, 0x87, 0x20, 0x68,
    0xe, 0x3, 0x83, 0xa3, 0x89, 0x83, 0xc0, 0xc0,
    0x0,

    /* U+F04C "" */
    0xf7, 0x95, 0x95, 0x95, 0x95, 0x95, 0x95, 0x95,
    0x95, 0xf7,

    /* U+F04D "" */
    0xff, 0xe0, 0x18, 0x6, 0x1, 0x80, 0x60, 0x18,
    0x6, 0x1, 0x80, 0x7f, 0xf0,

    /* U+F051 "" */
    0xc3, 0xc6, 0xec, 0x78, 0x70, 0xe3, 0xdd, 0xe3,
    0x84,

    /* U+F060 "" */
    0x4, 0x0, 0xc0, 0x18, 0x3, 0x0, 0x60, 0xf,
    0xff, 0x60, 0x3, 0x0, 0x18, 0x0, 0xc0, 0x4,
    0x0,

    /* U+F061 "" */
    0x0, 0x0, 0x30, 0x1, 0x80, 0xc, 0x0, 0x6f,
    0xff, 0x0, 0x60, 0xc, 0x1, 0x80, 0x30, 0x0,
    0x0,

    /* U+F062 "" */
    0xe, 0x1, 0xc0, 0x54, 0x12, 0xc6, 0x4c, 0x88,
    0x81, 0x0, 0x20, 0x4, 0x0, 0x80, 0x10, 0x2,
    0x0,

    /* U+F063 "" */
    0x4, 0x0, 0x80, 0x10, 0x2, 0x0, 0x40, 0x8,
    0x31, 0x1b, 0x26, 0x35, 0x83, 0xe0, 0x38, 0x2,
    0x0,

    /* U+F071 "" */
    0x3, 0x0, 0x1e, 0x1, 0x48, 0x2, 0x90, 0x1a,
    0x61, 0xc8, 0x82, 0x21, 0x18, 0x6, 0x42, 0xb,
    0x8, 0x38, 0x0, 0x7f, 0xff,

    /* U+F075 "" */
    0xf, 0xc0, 0xc0, 0xc4, 0x0, 0xb0, 0x3, 0x80,
    0x6, 0x0, 0x18, 0x0, 0x70, 0x3, 0x40, 0x9,
    0x0, 0xcf, 0xfc, 0x30, 0x0,

    /* U+F0AC "" */
    0xf, 0xc0, 0xf3, 0xc6, 0xc9, 0x92, 0x12, 0xff,
    0xfe, 0x21, 0x18, 0x84, 0x62, 0x11, 0xff, 0xfd,
    0x21, 0x26, 0x49, 0x8f, 0x3c, 0x1f, 0xe0,

    /* U+F0F3 "" */
    0x6, 0x0, 0xf0, 0x10, 0x82, 0x4, 0x20, 0x42,
    0x4, 0x20, 0x42, 0x4, 0x60, 0x64, 0x2, 0xff,
    0xf0, 0x0, 0xf, 0x0, 0x60,

    /* U+F118 "" */
    0xf, 0xc0, 0x61, 0x82, 0x1, 0x10, 0x2, 0xc8,
    0xce, 0x23, 0x18, 0x0, 0x60, 0x1, 0x80, 0x7,
    0x21, 0x34, 0x78, 0x88, 0x4, 0x18, 0x60, 0x3f,
    0x0,

    /* U+F124 "" */
    0x0, 0x20, 0xf, 0x7, 0xa1, 0xe6, 0x78, 0x4f,
    0xec, 0x2, 0xc0, 0x38, 0x3, 0x80, 0x30, 0x3,
    0x0, 0x20,

    /* U+F1EB "" */
    0x7, 0xf8, 0xe, 0x3, 0x86, 0x0, 0x3b, 0x0,
    0x2, 0x0, 0x0, 0x0, 0xfc, 0x0, 0xc1, 0xc0,
    0x40, 0x10, 0x0, 0x0, 0x0, 0x70, 0x0, 0x1c,
    0x0, 0x2, 0x0,

    /* U+F1F8 "" */
    0xf, 0x81, 0x88, 0xff, 0xf4, 0x2, 0x40, 0x24,
    0x2, 0x60, 0x26, 0x2, 0x60, 0x26, 0x2, 0x60,
    0x62, 0x6, 0x20, 0x63, 0xfc,

    /* U+F240 "" */
    0x7f, 0xfc, 0x80, 0x2, 0xbf, 0xf3, 0xbf, 0xf3,
    0xbf, 0xf3, 0x80, 0x2, 0x80, 0x2, 0x7f, 0xfc,

    /* U+F241 "" */
    0x7f, 0xfc, 0x80, 0x2, 0xbf, 0x83, 0xbf, 0x83,
    0xbf, 0x83, 0x80, 0x2, 0x80, 0x2, 0x7f, 0xfc,

    /* U+F242 "" */
    0x7f, 0xfc, 0x80, 0x2, 0x9f, 0x3, 0x9f, 0x3,
    0x9f, 0x3, 0x80, 0x2, 0x80, 0x2, 0x7f, 0xfc,

    /* U+F243 "" */
    0x7f, 0xfc, 0x80, 0x2, 0xb8, 0x3, 0xb8, 0x3,
    0xb8, 0x3, 0x80, 0x2, 0x80, 0x2, 0x7f, 0xfc,

    /* U+F244 "" */
    0x7f, 0xfc, 0x80, 0x2, 0x80, 0x3, 0x80, 0x3,
    0x80, 0x3, 0x80, 0x2, 0x80, 0x2, 0x7f, 0xfc,

    /* U+F293 "" */
    0xc, 0x7, 0x2, 0xd9, 0x36, 0xb1, 0xf0, 0x70,
    0x38, 0x7f, 0x64, 0xc2, 0xc1, 0xc0, 0xc0, 0x40,

    /* U+F376 "" */
    0x7e, 0xdc, 0x81, 0x82, 0x83, 0x83, 0x87, 0xc3,
    0x87, 0xc3, 0x83, 0x82, 0x83, 0x2, 0x76, 0xfc,

    /* U+F377 "" */
    0x80, 0x0, 0x30, 0x0, 0x3, 0x0, 0x0, 0x7f,
    0xf8, 0x4e, 0x1, 0x10, 0xc0, 0x64, 0x18, 0x19,
    0x3, 0x86, 0x40, 0x31, 0x10, 0x7, 0x43, 0xfe,
    0x60, 0x0, 0xc, 0x0, 0x1, 0xc0, 0x0, 0x10,

    /* U+F3C5 "" */
    0x1e, 0x18, 0x64, 0xa, 0x31, 0x92, 0x64, 0x98,
    0xc5, 0x2, 0x40, 0x88, 0x43, 0x30, 0x48, 0x1c,
    0x3, 0x0,

    /* U+F4DA "" */
    0xf, 0xc0, 0x61, 0x82, 0x1, 0x10, 0x2, 0xc8,
    0xce, 0x22, 0x98, 0x0, 0x60, 0x1, 0x80, 0x7,
    0x21, 0x34, 0x78, 0x88, 0x4, 0x18, 0x60, 0x3f,
    0x0,

    /* U+F556 "" */
    0xf, 0xc0, 0x61, 0x82, 0x1, 0x10, 0x2, 0xc0,
    0xe, 0x73, 0x98, 0x84, 0x60, 0x1, 0x80, 0x7,
    0xc, 0x34, 0x78, 0x88, 0x4, 0x18, 0x60, 0x3f,
    0x0,

    /* U+F579 "" */
    0xf, 0xc0, 0x61, 0xc2, 0x1, 0x90, 0x2, 0xdc,
    0xee, 0x94, 0x5b, 0xb5, 0x67, 0x39, 0x80, 0x7,
    0x3e, 0x34, 0x0, 0x88, 0x6, 0x18, 0x70, 0x3f,
    0x0,

    /* U+F584 "" */
    0xf, 0xc0, 0x61, 0x82, 0x1, 0x10, 0x2, 0xc4,
    0x8e, 0x73, 0x98, 0xcc, 0x60, 0x1, 0x80, 0x7,
    0x3f, 0x34, 0x78, 0x88, 0x4, 0x18, 0x60, 0x3f,
    0x0,

    /* U+F588 "" */
    0x3, 0xf0, 0x1, 0x86, 0x1, 0x80, 0x60, 0x40,
    0x8, 0x33, 0x33, 0x9, 0xce, 0x40, 0x0, 0x3,
    0x80, 0x7, 0xe0, 0x1, 0xf4, 0xfc, 0xb1, 0x1e,
    0x20, 0x60, 0x18, 0xe, 0x1c, 0x0, 0xfc, 0x0,

    /* U+F598 "" */
    0xf, 0xc0, 0x60, 0x82, 0x1, 0x90, 0x2, 0x88,
    0x6, 0x23, 0x98, 0x0, 0x60, 0xc1, 0x83, 0x36,
    0xc, 0xf4, 0x33, 0xc8, 0xf, 0x10, 0x0, 0x3f,
    0x0,

    /* U+F59B "" */
    0xf, 0xc0, 0x61, 0x82, 0x1, 0x10, 0xa, 0xd8,
    0x6e, 0x33, 0x19, 0x2, 0x60, 0x1, 0x8f, 0xe7,
    0x3f, 0x34, 0x78, 0x88, 0x4, 0x18, 0x60, 0x3f,
    0x0,

    /* U+F5A4 "" */
    0xf, 0xc0, 0x61, 0x82, 0x1, 0x10, 0x2, 0xc8,
    0xce, 0x23, 0x18, 0x0, 0x60, 0x1, 0x80, 0x7,
    0x0, 0x34, 0x0, 0x88, 0x4, 0x18, 0x60, 0x3f,
    0x0,

    /* U+F5B3 "" */
    0xf, 0xc0, 0x40, 0x82, 0x1, 0x10, 0x2, 0x80,
    0x6, 0x73, 0x98, 0x0, 0x65, 0xc9, 0xd7, 0x2f,
    0x5c, 0xb7, 0x3, 0x8c, 0xc, 0x18, 0x60, 0x3f,
    0x0,

    /* U+F68C "" */
    0xf0,

    /* U+F68D "" */
    0x8, 0x63, 0x18, 0xc4,

    /* U+F68E "" */
    0x0, 0x80, 0x40, 0x21, 0x10, 0x8c, 0x46, 0x23,
    0x11, 0x88, 0x80,

    /* U+F68F "" */
    0x0, 0x10, 0x1, 0x0, 0x10, 0x11, 0x1, 0x10,
    0x11, 0x9, 0x10, 0x91, 0x89, 0x18, 0x91, 0x89,
    0x18, 0x91,

    /* U+F695 "" */
    0xc0, 0x1, 0x30, 0x0, 0x8c, 0x2, 0x43, 0x1,
    0x20, 0x60, 0x90, 0x1c, 0x48, 0x6, 0x24, 0x1,
    0xd2, 0x4, 0x39, 0x2, 0x4c, 0x91, 0x23, 0xc8,
    0x90, 0xe4, 0x48, 0x9a, 0x24, 0x44,

    /* U+F6A8 "" */
    0x3, 0x0, 0xe, 0x0, 0x34, 0x33, 0xc8, 0x3f,
    0x13, 0x30, 0x22, 0x60, 0x44, 0xf8, 0x99, 0x79,
    0x4, 0x1a, 0x18, 0x1c, 0x0, 0x18, 0x0,

    /* U+F6A9 "" */
    0x3, 0x0, 0xe, 0x0, 0x34, 0x3, 0xc9, 0x1f,
    0x13, 0x70, 0x23, 0xa0, 0x47, 0x78, 0x9b, 0x79,
    0x22, 0x1a, 0x0, 0x1c, 0x0, 0x18, 0x0,

    /* U+F6AA "" */
    0xfd, 0x0,

    /* U+F6AB "" */
    0x1f, 0x86, 0xe, 0x80, 0x20, 0x0, 0x6, 0x0,
    0xe0, 0x6, 0x0,

    /* U+F6AC "" */
    0x80, 0x0, 0x37, 0xfc, 0xe, 0x3, 0x93, 0x0,
    0x78, 0xe0, 0x8, 0x1c, 0x0, 0x27, 0xc0, 0x39,
    0xb8, 0x10, 0x34, 0x0, 0xc, 0x0, 0x73, 0x0,
    0x38, 0xe0, 0x8, 0x18, 0x0, 0x4,

    /* U+F7C2 "" */
    0x1f, 0xc8, 0x14, 0x96, 0x35, 0x80, 0x60, 0x18,
    0x6, 0x1, 0x80, 0x60, 0x18, 0x6, 0x1, 0x80,
    0x7f, 0xf0
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 196, .box_w = 12, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 21, .adv_w = 224, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 46, .adv_w = 224, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 71, .adv_w = 224, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 96, .adv_w = 224, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 121, .adv_w = 224, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 146, .adv_w = 224, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 171, .adv_w = 224, .box_w = 14, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 198, .adv_w = 224, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 223, .adv_w = 224, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 248, .adv_w = 224, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 273, .adv_w = 224, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 298, .adv_w = 224, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 323, .adv_w = 224, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 348, .adv_w = 196, .box_w = 12, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 369, .adv_w = 196, .box_w = 12, .box_h = 9, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 383, .adv_w = 168, .box_w = 9, .box_h = 9, .ofs_x = 1, .ofs_y = 1},
    {.bitmap_index = 394, .adv_w = 224, .box_w = 13, .box_h = 13, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 416, .adv_w = 280, .box_w = 15, .box_h = 14, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 443, .adv_w = 224, .box_w = 13, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 466, .adv_w = 252, .box_w = 16, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 496, .adv_w = 224, .box_w = 13, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 519, .adv_w = 196, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 537, .adv_w = 280, .box_w = 17, .box_h = 13, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 565, .adv_w = 224, .box_w = 14, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 586, .adv_w = 224, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 611, .adv_w = 140, .box_w = 7, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 620, .adv_w = 168, .box_w = 10, .box_h = 13, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 637, .adv_w = 140, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 647, .adv_w = 168, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 660, .adv_w = 140, .box_w = 7, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 669, .adv_w = 196, .box_w = 12, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 686, .adv_w = 196, .box_w = 12, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 703, .adv_w = 168, .box_w = 11, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 720, .adv_w = 168, .box_w = 11, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 737, .adv_w = 224, .box_w = 14, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 758, .adv_w = 224, .box_w = 14, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 779, .adv_w = 224, .box_w = 14, .box_h = 13, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 802, .adv_w = 196, .box_w = 12, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 823, .adv_w = 224, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 848, .adv_w = 196, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 866, .adv_w = 280, .box_w = 18, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 893, .adv_w = 196, .box_w = 12, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 914, .adv_w = 252, .box_w = 16, .box_h = 8, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 930, .adv_w = 252, .box_w = 16, .box_h = 8, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 946, .adv_w = 252, .box_w = 16, .box_h = 8, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 962, .adv_w = 252, .box_w = 16, .box_h = 8, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 978, .adv_w = 252, .box_w = 16, .box_h = 8, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 994, .adv_w = 168, .box_w = 9, .box_h = 14, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 1010, .adv_w = 252, .box_w = 16, .box_h = 8, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 1026, .adv_w = 280, .box_w = 18, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 1058, .adv_w = 168, .box_w = 10, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 1076, .adv_w = 224, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 1101, .adv_w = 224, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 1126, .adv_w = 224, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 1151, .adv_w = 224, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 1176, .adv_w = 280, .box_w = 18, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 1208, .adv_w = 224, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 1233, .adv_w = 224, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 1258, .adv_w = 224, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 1283, .adv_w = 224, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 1308, .adv_w = 280, .box_w = 1, .box_h = 4, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 1309, .adv_w = 280, .box_w = 5, .box_h = 6, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 1313, .adv_w = 280, .box_w = 9, .box_h = 9, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 1324, .adv_w = 280, .box_w = 12, .box_h = 12, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 1342, .adv_w = 280, .box_w = 17, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 1372, .adv_w = 252, .box_w = 15, .box_h = 12, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 1395, .adv_w = 252, .box_w = 15, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 1418, .adv_w = 280, .box_w = 3, .box_h = 3, .ofs_x = 7, .ofs_y = -1},
    {.bitmap_index = 1420, .adv_w = 280, .box_w = 12, .box_h = 7, .ofs_x = 3, .ofs_y = -1},
    {.bitmap_index = 1431, .adv_w = 280, .box_w = 17, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 1461, .adv_w = 168, .box_w = 10, .box_h = 14, .ofs_x = 0, .ofs_y = -2}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_0[] = {
    0x0, 0x1a1, 0x320, 0x322, 0x327, 0x32a, 0x339, 0x342,
    0x347, 0x34d, 0x350, 0x359, 0x3be, 0xfb6, 0xfbc, 0xfc1,
    0xfc2, 0xfc6, 0xfc7, 0xfc8, 0xfca, 0xfce, 0xfdc, 0xfdd,
    0xff3, 0xff9, 0xffd, 0x1000, 0x1001, 0x1002, 0x1006, 0x1015,
    0x1016, 0x1017, 0x1018, 0x1026, 0x102a, 0x1061, 0x10a8, 0x10cd,
    0x10d9, 0x11a0, 0x11ad, 0x11f5, 0x11f6, 0x11f7, 0x11f8, 0x11f9,
    0x1248, 0x132b, 0x132c, 0x137a, 0x148f, 0x150b, 0x152e, 0x1539,
    0x153d, 0x154d, 0x1550, 0x1559, 0x1568, 0x1641, 0x1642, 0x1643,
    0x1644, 0x164a, 0x165d, 0x165e, 0x165f, 0x1660, 0x1661, 0x1777
};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] =
{
    {
        .range_start = 57419, .range_length = 6008, .glyph_id_start = 1,
        .unicode_list = unicode_list_0, .glyph_id_ofs_list = NULL, .list_length = 72, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    }
};



/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LVGL_VERSION_MAJOR == 8
/*Store all the custom data of the font*/
static  lv_font_fmt_txt_glyph_cache_t cache;
#endif

#if LVGL_VERSION_MAJOR >= 8
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = NULL,
    .kern_scale = 0,
    .cmap_num = 1,
    .bpp = 1,
    .kern_classes = 0,
    .bitmap_format = 0,
#if LVGL_VERSION_MAJOR == 8
    .cache = &cache
#endif
};



/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LVGL_VERSION_MAJOR >= 8
const lv_font_t font_awesome_14_1 = {
#else
lv_font_t font_awesome_14_1 = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 15,          /*The maximum line height required by the font*/
    .base_line = 2,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -1,
    .underline_thickness = 1,
#endif
    .dsc = &font_dsc,          /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
#if LV_VERSION_CHECK(8, 2, 0) || LVGL_VERSION_MAJOR >= 9
    .fallback = NULL,
#endif
    .user_data = NULL,
};



#endif /*#if FONT_AWESOME_14_1*/

