#ifndef _APPLICATION_H_
#define _APPLICATION_H_

#include <freertos/FreeRTOS.h>
#include <freertos/event_groups.h>
#include <freertos/task.h>

#include <string>
#include <mutex>
#include <list>
#include <deque>
#include <array>
#include <atomic>
#include <chrono>
#include <audio_decode_queue.h>
#include <opus_encoder.h>
#include <opus_decoder.h>
#include <opus_resampler.h>

#include "protocol.h"
#include "ota.h"
#include "background_task.h"

#include "esp_rom_sys.h"  //微秒 级别 延时包
#include "esp_wifi.h"

//全局宏定义管理
#include "project_config.h"

#if CONFIG_IDF_TARGET_ESP32S3
#include "wake_word_detect.h"
#include "audio_processor.h"
#endif


#define SCHEDULE_EVENT (1 << 0)
#define AUDIO_INPUT_READY_EVENT (1 << 1)
#define AUDIO_OUTPUT_READY_EVENT (1 << 2)

enum DeviceState {
    kDeviceStateUnknown,
    kDeviceStateStarting,
    kDeviceStateWifiConfiguring,
    kDeviceStateIdle,
    kDeviceStateConnecting,
    kDeviceStateListening,
    kDeviceStateSpeaking,
    kDeviceStateUpgrading,
    kDeviceStateMoaning,
    kDeviceStateOffline,
    kDeviceStateFatalError

    

};

#define OPUS_FRAME_DURATION_MS 60  

class Application {
public:
    static Application& GetInstance() {
        static Application instance;
        return instance;
    }
    // 删除拷贝构造函数和赋值运算符
    Application(const Application&) = delete;
    Application& operator=(const Application&) = delete;

    void Start();
    DeviceState GetDeviceState() const { return device_state_; }
    bool GetDeviceLowBState() const { return batter_state_; }
    bool IsVoiceDetected() const { return voice_detected_; }
    void Schedule(std::function<void()> callback);
    void SetDeviceState(DeviceState state);
    void Alert(const std::string& title, const std::string& message);
    void AbortSpeaking(AbortReason reason);
    void ToggleChatState();
    void StartListening();
    void StopListening();
    void UpdateIotStates();
    void CloseAudioChannel();
    std::vector<int16_t> ApplySpeedUp(const std::vector<int16_t>& pcm, float speed_factor);

    bool moaning_flag_ = false; 
    void MonitorStopAndAudioQueue();
    std::atomic<bool> mqtt_stop_flag_{false};  
    
    #if WIFI_SIGNAL_CHECK_TONE == 1
    Protocol* GetProtocol() { return protocol_.get(); }
    std::unique_ptr<AudioDecodeQueue>& GetAudioDecodeQueue() { return audio_decode_queue_; }
    #endif

    #if WIFI_CONNECT_CHECK_TONE == 1
    void PlayNetworkDisc();
    #endif

    


private:
    Application();
    ~Application();

    static constexpr int max_silence_seconds = 10;
    int current_volume_ = 80;
#if CONFIG_IDF_TARGET_ESP32S3
    WakeWordDetect wake_word_detect_;
    AudioProcessor audio_processor_;
#endif
    Ota ota_;
    std::mutex mutex_;
    std::list<std::function<void()>> main_tasks_;
    std::unique_ptr<Protocol> protocol_;
    EventGroupHandle_t event_group_;
    // std::deque<std::vector<uint8_t>> audio_decode_queue_;
    size_t total_buffered_size_{0};
    volatile DeviceState device_state_ = kDeviceStateUnknown;
    bool batter_state_ = false;
    bool keep_listening_ = false;
    bool aborted_ = false;
    bool voice_detected_ = false;
    std::string last_iot_states_;
    int idle_timeout_duration_ = 10;
    // Audio encode / decode
    BackgroundTask* background_task_ = nullptr;
    std::chrono::steady_clock::time_point last_output_time_;

    std::unique_ptr<OpusEncoderWrapper> opus_encoder_;
    // std::unique_ptr<OpusDecoderWrapper> opus_decoder_;

    int opus_decode_sample_rate_ = -1;
    OpusResampler input_resampler_;
    OpusResampler reference_resampler_;
    OpusResampler output_resampler_;
    std::unique_ptr<AudioDecodeQueue> audio_decode_queue_;
    // 音频输出缓冲相关
    std::chrono::steady_clock::time_point last_voice_time_;  // 添加这一行
    static const size_t BUFFER_COUNT = 3;
    std::array<std::vector<int16_t>, BUFFER_COUNT> audio_buffers_;
    std::atomic<bool> buffer_ready_[BUFFER_COUNT] = {false, false, false};
    int current_buffer_ = 0;

    std::atomic<bool> stop_after_playback_{false}; 
    std::chrono::steady_clock::time_point playback_start_time_;
    void ImuRead();
    void MainLoop();
    void InputAudioLoop();
    void InputAudio();
    void OutputAudio();
    void ResetDecoder();
    void SetDecodeSampleRate(int sample_rate);
    void CheckNewVersion();

#if WIFI_SIGNAL_CHECK_TONE == 1
    void MonitorWifiRssi();
    void HandleWifiWeak();
#endif

    // void PlayLocalFile(const char* data, size_t size);
    // 保存语言类型到 NVS
    void SaveLanguageTypeToNVS(const std::string& language);
    void SaveVolumeToNVS(int volume);
    int LoadVolumeFromNVS(int default_value);
    std::string GetLanguageFromNVS();
    std::pair<bool,std::string> moaning_state_ {false,""};
    void PlayOpusFile(const char* data, size_t size);
    static constexpr int kVolumeStep = 10;
    static constexpr int kVolumeMax = 100;
    static constexpr int kVolumeMin = 60;
    int silence_count_  =0;
    bool state_sepeaking_ = false;
    bool oudio_output_finish_ =true;
    bool audio_channel_close_=false;
    bool wake_stage_end_=false;
    uint32_t wake_stage_end_cnt_=0;
    double batter_volo_ = 4.2f; 
    // bool cancel_tts_sent_ = false; // 标记当前listening周期是否已发送CancelTTS
    bool start_play_voice_ =  false;
    int start_play_voice_num_  = 0;
    int touch_value_= 0;
    bool last_batter_state_  =false;

    std::chrono::steady_clock::time_point last_audio_received_time_;

    bool OFFLINE_FLAG = false;
    bool NetOff_OFFLINE_FLAG = false; 

    
    

};

#endif // _APPLICATION_H_
