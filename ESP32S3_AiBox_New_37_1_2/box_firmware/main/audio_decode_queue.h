#ifndef AUDIODECODEQUEUE_H
#define AUDIODECODEQUEUE_H
#include <map>
#include <queue>
#include <string>
#include <vector>
#include <opus_resampler.h>
#include <opus_decoder.h>
#include "audio_codec.h"
#include "esp_log.h"
#include <mutex>
#include <functional>
#include <chrono>

enum class AudioQueueMode {
    TTS,
    Local
};


class AudioDecodeQueue {
 public:
  AudioDecodeQueue(AudioCodec* codec, std::function<bool()> callback);

  void clear() {
    std::lock_guard<std::mutex> lock(mutex_);
    audio_decode_queue_.clear();
    circal_play_local_audio_output_ready_envet_ = false;
    circal_play_local_data_ = nullptr;
    size_t circal_play_local_data_size_ = 0;
    loop_local_audio_decode_queue_.clear();

    //等待预缓冲
    tts_waiting_for_buffer_ = true; 
    queue_mode_ = AudioQueueMode::TTS; 
    first_segment_ = true; 

  }
  //
  bool empty() {
    std::lock_guard<std::mutex> lock(mutex_);
    return audio_decode_queue_.empty() &&
           loop_local_audio_decode_queue_.empty();
  }
  //
  void push_back(std::vector<uint8_t>&& data);
  void FlushSegmentBuffer(); // 新增：flush分段缓冲

  void push_back_segment(std::vector<uint8_t>&& data);
  //
  void LoopPlayLocalFile(const char* data, size_t size);
  void PlayLocalFile(const char* data, size_t size);
  //

  void Stop();
  AudioCodec* codec_;
  std::function<bool()> stop_function_;

  // 线程安全判空
  bool SafeEmpty() {
      std::lock_guard<std::mutex> lock(mutex_);
      return audio_decode_queue_.empty() && loop_local_audio_decode_queue_.empty();
  }
  // 线程安全清空
  void SafeClear() {
      std::lock_guard<std::mutex> lock(mutex_);
      clear();
  }


 private:
  void AudioProcess();
  std::vector<std::vector<uint8_t>> FramesTodecode(
      std::deque<std::vector<uint8_t>>& data);
  void OutputAudio(const std::vector<std::vector<uint8_t>>&);
  bool OutputAudioSelect(std::deque<std::vector<uint8_t>>&);
  std::mutex mutex_;
  float speed_factor_ = 1.0f;
  EventGroupHandle_t event_group_;
  std::unique_ptr<OpusDecoderWrapper> opus_decoder_;
  std::deque<std::vector<uint8_t>> audio_decode_queue_;
  std::vector<std::vector<uint8_t>> loop_local_audio_decode_queue_;
  bool circal_play_local_audio_output_ready_envet_ = false;
  char* circal_play_local_data_ = nullptr;
  size_t circal_play_local_data_size_ = 0;
  void CirclePlayData();

  bool tts_waiting_for_buffer_ = false;
  AudioQueueMode queue_mode_ = AudioQueueMode::TTS; 

  static constexpr size_t kSegmentFrameCount = 8; 
  std::deque<std::vector<uint8_t>> segment_buffer_; // 新增：分段缓冲

  bool first_segment_ = true; // 首包标志
};
//
#endif
