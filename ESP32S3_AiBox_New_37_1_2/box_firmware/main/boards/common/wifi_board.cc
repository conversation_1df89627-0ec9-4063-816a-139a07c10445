#include "wifi_board.h"

#include "display.h"
#include "application.h"
#include "system_info.h"
#include "font_awesome_symbols.h"
#include "settings.h"

#include <freertos/FreeRTOS.h>
#include <freertos/task.h>
#include <esp_http.h>
#include <esp_mqtt.h>
#include <esp_udp.h>
#include <tcp_transport.h>
#include <tls_transport.h>
#include <web_socket.h>
#include <esp_log.h>

#include <wifi_station.h>
#include <wifi_configuration_ap.h>
#include <ssid_manager.h>

//全局宏定义管理
#include "project_config.h"

static const char *TAG = "WifiBoard";

//添加wifi 未连接 播报提示音
// extern const char p3_NETWORKdisc_start[] asm("_binary_NETWORKdisc_p3_start");
// extern const char p3_NETWORKdisc_end[]   asm("_binary_NETWORKdisc_p3_end");


static std::string rssi_to_string(int rssi) {
    if (rssi >= -55) {
        return "Very good";
    } else if (rssi >= -65) {
        return "Good";
    } else if (rssi >= -75) {
        return "Fair";
    } else if (rssi >= -85) {
        return "Poor";
    } else {
        return "No network";
    }
}

WifiBoard::WifiBoard() {
    Settings settings("wifi", true);
    wifi_config_mode_ = settings.GetInt("force_ap") == 1;
    if (wifi_config_mode_) {
        ESP_LOGI(TAG, "force_ap is set to 1, reset to 0");
        settings.SetInt("force_ap", 0);
    }
}

void WifiBoard::EnterWifiConfigMode() {
    auto& application = Application::GetInstance();
    auto display = Board::GetInstance().GetDisplay();
    application.SetDeviceState(kDeviceStateWifiConfiguring);

    auto& wifi_ap = WifiConfigurationAp::GetInstance();
    wifi_ap.SetSsidPrefix("doll");
    wifi_ap.Start();
    
    // wifi连接音
    //application.Alert("Info", "Configuring WiFi");

    // 显示 WiFi 配置 AP 的 SSID 和 Web 服务器 URL
    std::string hint = "请在手机上连接热点 ";
    hint += wifi_ap.GetSsid();
    hint += "，然后打开浏览器访问 ";
    hint += wifi_ap.GetWebServerUrl();

    display->SetStatus(hint);
    

    #if 1 //-----------新方法 在while中判断 每6秒播放一次提示音

        //------------方法四(正在使用)
        while (true) {
            // 检查AP下是否有STA连接
            wifi_sta_list_t sta_list;
            bool has_sta = false;
            if (esp_wifi_ap_get_sta_list(&sta_list) == ESP_OK) {
                has_sta = (sta_list.num > 0); //has_sta ture 为 有连接 
            }

            if (!has_sta) {
                // 没有STA连接，循环播报提示音
                uint16_t now = xTaskGetTickCount();
                static uint16_t last_play_time = 0;
                static uint16_t last_log_time = 0;

                if (now - last_play_time > 9000 / portTICK_PERIOD_MS) {
                    application.Alert("Info", "Configuring WiFi");
                    last_play_time = now;
                }
                if (now - last_log_time > 10000 / portTICK_PERIOD_MS) {
                    int free_sram = heap_caps_get_free_size(MALLOC_CAP_INTERNAL);
                    int min_free_sram = heap_caps_get_minimum_free_size(MALLOC_CAP_INTERNAL);
                    ESP_LOGI(TAG, "mac:id1--%s,id2--%s ,id3--%s",
                            SystemInfo::GetMacAddress().c_str(),
                            SystemInfo::GetMacAddressNoColon().c_str(),
                            SystemInfo::GetMacAddressDecimal().c_str());
                    ESP_LOGI(TAG, "Free internal: %u minimal internal: %u", free_sram, min_free_sram);
                    last_log_time = now;
                }
            } else {
                // 有STA连接，清空音频队列，暂停播报
                Application::GetInstance().GetAudioDecodeQueue()->clear();
                // 你可以选择在这里等待STA断开
                // 也可以什么都不做，直接进入下一个循环
            }

            vTaskDelay(pdMS_TO_TICKS(200));
        }




        //------------方法三
        // uint16_t last_play_time = 0;
        // uint16_t last_log_time = 0;
        // const uint16_t play_interval = 6000; // 6秒
        // const uint16_t log_interval = 10000; // 10秒

        // while (true) {
        //     // 检查AP下是否有STA连接
        //     wifi_sta_list_t sta_list;
        //     if (esp_wifi_ap_get_sta_list(&sta_list) == ESP_OK) {
        //         if (sta_list.num > 0) {
        //             break;
        //         }
        //     }

        //     uint16_t now = xTaskGetTickCount() * portTICK_PERIOD_MS;

        //     // 每6秒播报一次提示音
        //     if (now - last_play_time > play_interval) {
        //         application.Alert("Info", "Configuring WiFi");
        //         last_play_time = now;
        //     }

        //     // 每10秒打印一次系统信息
        //     if (now - last_log_time > log_interval) {
        //         int free_sram = heap_caps_get_free_size(MALLOC_CAP_INTERNAL);
        //         int min_free_sram = heap_caps_get_minimum_free_size(MALLOC_CAP_INTERNAL);
        //         ESP_LOGI(TAG, "mac:id1--%s,id2--%s ,id3--%s",
        //                 SystemInfo::GetMacAddress().c_str(),
        //                 SystemInfo::GetMacAddressNoColon().c_str(),
        //                 SystemInfo::GetMacAddressDecimal().c_str());
        //         ESP_LOGI(TAG, "Free internal: %u minimal internal: %u", free_sram, min_free_sram);
        //         last_log_time = now;
        //     }

        //     vTaskDelay(pdMS_TO_TICKS(200)); // 200ms循环一次，保证响应快
        // }




        //------------方法二
        // while (true) {
        //     // 检查AP下是否有STA连接
        //     wifi_sta_list_t sta_list;
        //     if (esp_wifi_ap_get_sta_list(&sta_list) == ESP_OK) {
        //         if (sta_list.num > 0) {
        //             break;
        //         }
        //     }

        //     application.Alert("Info", "Configuring WiFi");
        //     // 等待6秒再播下一次
        //     vTaskDelay(pdMS_TO_TICKS(6000));

        //     // 每10秒打印一次系统信息（可选，建议单独计时）
        //     // ...你的日志打印逻辑...


        //     int free_sram = heap_caps_get_free_size(MALLOC_CAP_INTERNAL);
        //     int min_free_sram = heap_caps_get_minimum_free_size(MALLOC_CAP_INTERNAL);
        //     ESP_LOGI(TAG, "mac:id1--%s,id2--%s ,id3--%s",
        //             SystemInfo::GetMacAddress().c_str(),
        //             SystemInfo::GetMacAddressNoColon().c_str(),
        //             SystemInfo::GetMacAddressDecimal().c_str());

        //     ESP_LOGI(TAG, "Free internal: %u minimal internal: %u", free_sram, min_free_sram);
        //     vTaskDelay(pdMS_TO_TICKS(4000));

        // }


        //------------方法一（推荐保底）
        // uint16_t last_play_time = 0;
        // uint16_t last_log_time = 0;
        // while (true) {
        //     // 检查AP下是否有STA连接
        //     wifi_sta_list_t sta_list;
        //     if (esp_wifi_ap_get_sta_list(&sta_list) == ESP_OK) {
        //         if (sta_list.num > 0) {
        //             // 有手机连接到AP，停止播报
        //             Application::GetInstance().GetAudioDecodeQueue()->clear(); //清空音频队列
        //             break;
        //         }
        //     }

        //     uint16_t now = xTaskGetTickCount();

        //     // 每6秒播报一次提示音
        //     if (now - last_play_time > 9000 / portTICK_PERIOD_MS) {
        //         application.Alert("Info", "Configuring WiFi");
        //         last_play_time = now;
        //     }

        //     // 每10秒打印一次系统信息
        //     if (now - last_log_time > 10000 / portTICK_PERIOD_MS) {
        //         int free_sram = heap_caps_get_free_size(MALLOC_CAP_INTERNAL);
        //         int min_free_sram = heap_caps_get_minimum_free_size(MALLOC_CAP_INTERNAL);
        //         ESP_LOGI(TAG, "mac:id1--%s,id2--%s ,id3--%s",
        //                 SystemInfo::GetMacAddress().c_str(),
        //                 SystemInfo::GetMacAddressNoColon().c_str(),
        //                 SystemInfo::GetMacAddressDecimal().c_str());
        //         ESP_LOGI(TAG, "Free internal: %u minimal internal: %u", free_sram, min_free_sram);
        //         last_log_time = now;
        //     }

        //     vTaskDelay(pdMS_TO_TICKS(200)); // 200ms循环一次
        // }

    #else//-----------原版方法

    // Wait forever until reset after configuration
    while (true) {
        int free_sram = heap_caps_get_free_size(MALLOC_CAP_INTERNAL);
        int min_free_sram = heap_caps_get_minimum_free_size(MALLOC_CAP_INTERNAL);
        ESP_LOGI(TAG, "mac:id1--%s,id2--%s ,id3--%s",
                 SystemInfo::GetMacAddress().c_str(),
                 SystemInfo::GetMacAddressNoColon().c_str(),
                 SystemInfo::GetMacAddressDecimal().c_str());

        ESP_LOGI(TAG, "Free internal: %u minimal internal: %u", free_sram, min_free_sram);
        vTaskDelay(pdMS_TO_TICKS(10000));
    }

    #endif
}

void WifiBoard::StartNetwork() {
    // User can press BOOT button while starting to enter WiFi configuration mode
    if (wifi_config_mode_) {
        EnterWifiConfigMode();
        return;
    }

    // If no WiFi SSID is configured, enter WiFi configuration mode
    //如果设有配置wiFi SSID,请进入WiFi配置模式
    auto& ssid_manager = SsidManager::GetInstance();
    auto ssid_list = ssid_manager.GetSsidList();
    if (ssid_list.empty()) {
        wifi_config_mode_ = true;
        EnterWifiConfigMode();
        return;
    }

    auto& wifi_station = WifiStation::GetInstance();
    wifi_station.OnScanBegin([this]() {

        
        auto display = Board::GetInstance().GetDisplay();
        display->ShowNotification("正在扫描 WiFi 网络", 30000);

    });
    wifi_station.OnConnect([this](const std::string& ssid) {

        
        auto display = Board::GetInstance().GetDisplay();
        display->ShowNotification(std::string("正在连接 ") + ssid, 30000);

    });
    wifi_station.OnConnected([this](const std::string& ssid) {

       
        auto display = Board::GetInstance().GetDisplay();
        display->ShowNotification(std::string("已连接 ") + ssid);
    

    #if WIFI_SIGNAL_CHECK_TONE == 9
        auto& app = Application::GetInstance();
        Protocol* protocol = app.GetProtocol();
        if (protocol) {
            
            // 如果 MQTT 客户端未连接，则尝试启动
            if (!protocol->IsMqttConnected()) {

                #if 1
                    static uint16_t last_retry = 0;
                    uint16_t now = xTaskGetTickCount();
                    if (now - last_retry > 2000 / portTICK_PERIOD_MS) { // 3秒重试一次
                        last_retry = now;
                        app.Schedule([&app](){
                            if (app.GetAudioDecodeQueue()) app.GetAudioDecodeQueue()->clear();
                        });
                        protocol->StartMqttClient();
                    }


                #else
                
                app.Schedule([&app](){
                    if (app.GetAudioDecodeQueue()) app.GetAudioDecodeQueue()->clear();
                });
                protocol->StartMqttClient(); ////重启Mqtt客户端
                ESP_LOGW("START_MQTT", "CONNECTED WIFI , START_MQTT_CLIENT ------>001");
                
                #endif
            
            }
        }
    #endif
       
    
    });
    wifi_station.Start();


    #if WIFI_CONNECT_CHECK_TONE == 1 
        uint16_t total_wait = 0;
        const uint16_t interval = 6000; 
        const uint16_t max_wait = 60000; 
        
        while (!wifi_station.IsConnected() && total_wait < max_wait) {
            Application::GetInstance().Schedule([]() {
                Application::GetInstance().PlayNetworkDisc();
                ESP_LOGE("wifi", "------------------ wifi disconnect ------------------");
                vTaskDelay(pdMS_TO_TICKS(3000));
            });
            wifi_station.WaitForConnected(interval); // 等待3秒
            total_wait += interval;
        }


        if (!wifi_station.IsConnected()) {
            wifi_station.Stop();
            wifi_config_mode_ = true;
            EnterWifiConfigMode();
            return;
        }


    #elif WIFI_CONNECT_CHECK_TONE == 0
        // Try to connect to WiFi, if failed, launch the WiFi configuration AP
        if (!wifi_station.WaitForConnected(60 * 1000)) {
            wifi_station.Stop();
            wifi_config_mode_ = true;
            EnterWifiConfigMode();
            return;
        }

    #endif
}

Http* WifiBoard::CreateHttp() {
    return new EspHttp();
}

WebSocket* WifiBoard::CreateWebSocket() {
#ifdef CONFIG_CONNECTION_TYPE_WEBSOCKET
    std::string url = CONFIG_WEBSOCKET_URL;
    if (url.find("wss://") == 0) {
        return new WebSocket(new TlsTransport());
    } else {
        return new WebSocket(new TcpTransport());
    }
#endif
    return nullptr;
}

Mqtt* WifiBoard::CreateMqtt() {
    return new EspMqtt();
}

Udp* WifiBoard::CreateUdp() {
    return new EspUdp();
}

bool WifiBoard::GetNetworkState(std::string& network_name, int& signal_quality, std::string& signal_quality_text) {
    if (wifi_config_mode_) {
        auto& wifi_ap = WifiConfigurationAp::GetInstance();
        network_name = wifi_ap.GetSsid();
        signal_quality = -99;
        signal_quality_text = wifi_ap.GetWebServerUrl();
        return true;
    }
    auto& wifi_station = WifiStation::GetInstance();
    if (!wifi_station.IsConnected()) {
        return false;
    }
    network_name = wifi_station.GetSsid();
    signal_quality = wifi_station.GetRssi();
    signal_quality_text = rssi_to_string(signal_quality);
    return signal_quality != -1;
}

const char* WifiBoard::GetNetworkStateIcon() {
    if (wifi_config_mode_) {
        return FONT_AWESOME_WIFI;
    }
    auto& wifi_station = WifiStation::GetInstance();
    if (!wifi_station.IsConnected()) {
        return FONT_AWESOME_WIFI_OFF;
    }
    int8_t rssi = wifi_station.GetRssi();
    if (rssi >= -55) {
        return FONT_AWESOME_WIFI;
    } else if (rssi >= -65) {
        return FONT_AWESOME_WIFI_FAIR;
    } else {
        return FONT_AWESOME_WIFI_WEAK;
    }
}

std::string WifiBoard::GetBoardJson() {
    // Set the board type for OTA
    auto& wifi_station = WifiStation::GetInstance();
    std::string board_type = BOARD_TYPE;
    std::string board_json = std::string("{\"type\":\"" + board_type + "\",");
    if (!wifi_config_mode_) {
        board_json += "\"ssid\":\"" + wifi_station.GetSsid() + "\",";
        board_json += "\"rssi\":" + std::to_string(wifi_station.GetRssi()) + ",";
        board_json += "\"channel\":" + std::to_string(wifi_station.GetChannel()) + ",";
        board_json += "\"ip\":\"" + wifi_station.GetIpAddress() + "\",";
    }
    board_json += "\"mac\":\"" + SystemInfo::GetMacAddress() + "\"}";
    return board_json;
}

void WifiBoard::SetPowerSaveMode(bool enabled) {
    auto& wifi_station = WifiStation::GetInstance();
    wifi_station.SetPowerSaveMode(enabled);
}

void WifiBoard::ResetWifiConfiguration() {
    // Reset the wifi station
    {
        Settings settings("wifi", true);
        settings.SetInt("force_ap", 1);
    }
    GetDisplay()->ShowNotification("进入配网模式...");
    vTaskDelay(pdMS_TO_TICKS(1000));
    // Reboot the device
    esp_restart();
}
