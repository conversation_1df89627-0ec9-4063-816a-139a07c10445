#include "button.h"
#include "driver/gpio.h"
#include <esp_log.h>

#include "board.h"
#include "esp_system.h"
static const char* TAG = "Button";

uint32_t kPoserOffsetCount =0 ;
Button::Button(gpio_num_t gpio_num,bool disable_pull1, bool active_high) : gpio_num_(gpio_num) {
    if (gpio_num == GPIO_NUM_NC) {
        return;
    }
    button_config_t button_config = {
        .type = BUTTON_TYPE_GPIO,
        .long_press_time = 3000,
        .short_press_time = 50,
        .gpio_button_config = {
            .gpio_num = gpio_num,
            .active_level = static_cast<uint8_t>(active_high ? 0 : 1),
            .disable_pull = disable_pull1  // 禁用内部上拉和下拉
        }
    };
    button_handle_ = iot_button_create(&button_config);




    {
      // 先放在里，这个口要控制电源关闭，假如电源块煤电。
      gpio_set_direction(GPIO_NUM_4, GPIO_MODE_OUTPUT);

      gpio_set_level(GPIO_NUM_4, 1);  // 1 表示高电平，0 表示低电平
                                      // 设置 GPIO23 为高电平
    }
    {
      //   gpio_config_t io_conf = {.pin_bit_mask = (1ULL << GPIO_NUM_48),
      //                            .mode = GPIO_MODE_INPUT,
      //                            .pull_up_en = GPIO_PULLUP_DISABLE,
      //                            .pull_down_en = GPIO_PULLDOWN_DISABLE,
      //                            .intr_type = GPIO_INTR_DISABLE};
      //   gpio_config(&io_conf);

      //   button_config_t button_config = {
      //       .type = BUTTON_TYPE_GPIO,
      //       .long_press_time = 3000,
      //       .short_press_time = 50,
      //       .gpio_button_config = {
      //           .gpio_num = GPIO_NUM_48,
      //           .active_level = static_cast<uint8_t>(active_high ? 0 : 1),
      //           .disable_pull = true  // 禁用内部上拉和下拉
      //       }};
      //  auto  button_handle_temp = iot_button_create(&button_config);
      gpio_set_direction(GPIO_NUM_48, GPIO_MODE_INPUT);
    }

    if (button_handle_ == NULL) {
      ESP_LOGE(TAG, "Failed to create button handle");
      return;
    }
}
//

void SetPowerOffset(bool a) {
  // 待机太常，语音关键，电量太低关机
  auto led = Board::GetInstance().GetLed();
  led->ShuntDown();
  gpio_set_level(GPIO_NUM_4, a);  // 1 表示高电平，0 表示低电平
}

//
Button::~Button() {
    if (button_handle_ != NULL) {
        iot_button_delete(button_handle_);
    }
}



void Button::OnPressDown(std::function<void()> callback) {
    if (button_handle_ == nullptr) {
        return;
    }
    on_press_down_ = callback;
    iot_button_register_cb(button_handle_, BUTTON_PRESS_DOWN, [](void* handle, void* usr_data) {
        Button* button = static_cast<Button*>(usr_data);
        if (button->on_press_down_) {
            ESP_LOGI(TAG, "Button Press Down Detected");

            button->on_press_down_();
           
        }
    }, this);
}

void Button::OnPressUp(std::function<void()> callback) {
    if (button_handle_ == nullptr) {
        return;
    }
    on_press_up_ = callback;
    iot_button_register_cb(button_handle_, BUTTON_PRESS_UP, [](void* handle, void* usr_data) {
        Button* button = static_cast<Button*>(usr_data);
        if (button->on_press_up_) {
            ESP_LOGI(TAG, "Button Press Up Detected");
            button->on_press_up_();
        }
    }, this);
}

void Button::OnLongPress(std::function<void()> callback) {
    if (button_handle_ == nullptr) {
        return;
    }
    on_long_press_ = callback;
    iot_button_register_cb(button_handle_, BUTTON_LONG_PRESS_HOLD, [](void* handle, void* usr_data) {
        Button* button = static_cast<Button*>(usr_data);
        if (button->on_long_press_) {
            ESP_LOGI(TAG, "Button Long Press Detected");
            button->on_long_press_();
        }
    }, this);
}

void Button::OnClick(std::function<void()> callback) {
    if (button_handle_ == nullptr) {
        return;
    }
    on_click_ = callback;
    iot_button_register_cb(button_handle_, BUTTON_SINGLE_CLICK, [](void* handle, void* usr_data) {
        Button* button = static_cast<Button*>(usr_data);
        if (button->on_click_) {
            ESP_LOGI(TAG, "Button Single Click Detected");
            button->on_click_();
        }
    }, this);
}

void Button::OnDoubleClick(std::function<void()> callback) {
    if (button_handle_ == nullptr) {
        return;
    }
    on_double_click_ = callback;
    iot_button_register_cb(button_handle_, BUTTON_DOUBLE_CLICK, [](void* handle, void* usr_data) {
        Button* button = static_cast<Button*>(usr_data);
        if (button->on_double_click_) {
            ESP_LOGI(TAG, "Button Double Click Detected");
            button->on_double_click_();
        }
    }, this);
}
