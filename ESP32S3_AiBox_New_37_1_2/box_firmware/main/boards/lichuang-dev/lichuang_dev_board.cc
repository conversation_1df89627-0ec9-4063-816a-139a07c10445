#include "wifi_board.h"
#include "audio_codecs/box_audio_codec.h"
#include "display/lcd_display.h"
#include "application.h"
#include "button.h"
#include "config.h"
#include "i2c_device.h"
#include "iot/thing_manager.h"
#include "driver/adc.h"
#include "esp_adc_cal.h"
#include <esp_log.h>
#include <esp_lcd_panel_vendor.h>
#include <driver/i2c_master.h>
#include <driver/spi_common.h>
#include <wifi_station.h>
#include <driver/gpio.h>
#include "esp_adc/adc_oneshot.h"
// Define SET_PCA at the top of the file to control conditional code blocks
#define EXAMPLE_ADC1_CHANNEL    ADC1_CHANNEL_7  // GPIO1
#define EXAMPLE_ADC_ATTEN       ADC_ATTEN_DB_11 // 衰减 11dB，支持较宽电压
#define EXAMPLE_ADC_WIDTH       ADC_WIDTH_BIT_12 // 分辨率为 12 位

#define TAG "LichuangDevBoard"

          //*************  F-5 month 23 day ----declaration  **************
          // uint8_t len = 0;
          // uint16_t times = 0;
          // unsigned char uart_433_data[UART_433_RX_Buffer_Size] = {0};
          //*****************************************

namespace{
  uint8_t qmi8658_device_addr = 0x6A;  // 默认地址
}

class LichuangDevBoard : public WifiBoard {
private:
    i2c_master_bus_handle_t i2c_bus_;
    Button boot_button_;
    std::vector<gpio_num_t> touch_buttons_pin_{GPIO_NUM_16,
                                               GPIO_NUM_17, GPIO_NUM_18,GPIO_NUM_8};
    //
    //
    std::vector<bool> touch_buttons_state_;
    //
    std::vector<std::shared_ptr< Button>> touch_buttons_;
    
    LcdDisplay* display_;
    
    int boot_click_count_ = 0;
    int64_t last_click_time_ = 0;
    static const int CLICK_TIMEOUT_MS = 1000; // 1秒内需要完成3次点击

    void InitializeI2c() {
        // Initialize I2C peripheral
        i2c_master_bus_config_t i2c_bus_cfg = {
            .i2c_port = (i2c_port_t)1,
            .sda_io_num = AUDIO_CODEC_I2C_SDA_PIN,
            .scl_io_num = AUDIO_CODEC_I2C_SCL_PIN,
            .clk_source = I2C_CLK_SRC_DEFAULT,
            .glitch_ignore_cnt = 7,
            .intr_priority = 0,
            .trans_queue_depth = 0,
            .flags = {
                .enable_internal_pullup = 1,
            },
        };
        ESP_ERROR_CHECK(i2c_new_master_bus(&i2c_bus_cfg, &i2c_bus_));

        // Configure GPIO_NUM_15 as output and set to high level
        gpio_config_t io_conf = {};
        io_conf.intr_type = GPIO_INTR_DISABLE;
        io_conf.mode = GPIO_MODE_OUTPUT;
        io_conf.pin_bit_mask = (1ULL << GPIO_NUM_15);
        io_conf.pull_down_en = GPIO_PULLDOWN_DISABLE;
        io_conf.pull_up_en = GPIO_PULLUP_DISABLE;
        ESP_ERROR_CHECK(gpio_config(&io_conf));
        
        // Set GPIO_NUM_15 to high level
        ESP_ERROR_CHECK(gpio_set_level(GPIO_NUM_15, 1));
    }

    void InitializeSpi() {
        spi_bus_config_t buscfg = {};
        buscfg.mosi_io_num = GPIO_NUM_40;
        buscfg.miso_io_num = GPIO_NUM_NC;
        buscfg.sclk_io_num = GPIO_NUM_41;
        buscfg.quadwp_io_num = GPIO_NUM_NC;
        buscfg.quadhd_io_num = GPIO_NUM_NC;
        buscfg.max_transfer_sz = DISPLAY_WIDTH * DISPLAY_HEIGHT * sizeof(uint16_t);
        ESP_ERROR_CHECK(spi_bus_initialize(SPI3_HOST, &buscfg, SPI_DMA_CH_AUTO));
    }

    std::vector<bool> GetTouchKey() { return touch_buttons_state_; };
    /*111111111111111111111111111111111111111111111111111*/
    void InitializeButtons() {
      for (int i = 0; i < touch_buttons_pin_.size(); i++) {
        //
        touch_buttons_.emplace_back(new Button(touch_buttons_pin_[i],false,true));
        //
        touch_buttons_state_.push_back(false);
        touch_buttons_.back()->OnPressDown(
            [this, i]() { touch_buttons_state_[i] = true; });
        touch_buttons_.back()->OnPressUp(
            [this, i]() { touch_buttons_state_[i] = false; });
      }
      //
      boot_button_.OnClick([this]() {
        int64_t current_time = esp_timer_get_time() / 1000;  // 转换为毫秒

        // 如果距离上次点击超过1秒，重置计数器
        if (current_time - last_click_time_ > CLICK_TIMEOUT_MS) {
          boot_click_count_ = 0;
        }

        boot_click_count_++;
        last_click_time_ = current_time;

        // 检查是否在1秒内完成了3次点击
        if (boot_click_count_ >= 3) {
          boot_click_count_ = 0;     // 重置计数器
          ResetWifiConfiguration();  // 直接进入配网模式，不检查设备状态
        }
      });

      // boot_button_.OnPressDown([this]() {
      //     ///Application::GetInstance().StartListening();
      // });

      // boot_button_.OnPressUp([this]() {
      //    // Application::GetInstance().StopListening();
      // });
      boot_button_.OnLongPress([this]() {
        ESP_LOGW(TAG, "Poweroff");
        SetPowerOffset(0);
      });
    }
      /*111111111111111111111111111111111111111111111111111*/
    
    void InitializeSt7789Display() {
        esp_lcd_panel_io_handle_t panel_io = nullptr;
        esp_lcd_panel_handle_t panel = nullptr;
        // 液晶屏控制IO初始化
        ESP_LOGD(TAG, "Install panel IO");
        esp_lcd_panel_io_spi_config_t io_config = {};
        io_config.cs_gpio_num = GPIO_NUM_NC;
        io_config.dc_gpio_num = GPIO_NUM_39;
        io_config.spi_mode = 2;
        io_config.pclk_hz = 80 * 1000 * 1000;
        io_config.trans_queue_depth = 10;
        io_config.lcd_cmd_bits = 8;
        io_config.lcd_param_bits = 8;
        ESP_ERROR_CHECK(esp_lcd_new_panel_io_spi(SPI3_HOST, &io_config, &panel_io));

        // 初始化液晶屏驱动芯片ST7789
        ESP_LOGD(TAG, "Install LCD driver");
        esp_lcd_panel_dev_config_t panel_config = {};
        panel_config.reset_gpio_num = GPIO_NUM_NC;
        panel_config.rgb_ele_order = LCD_RGB_ELEMENT_ORDER_RGB;
        panel_config.bits_per_pixel = 16;
        ESP_ERROR_CHECK(esp_lcd_new_panel_st7789(panel_io, &panel_config, &panel));
        
        esp_lcd_panel_reset(panel);
        esp_lcd_panel_init(panel);
        esp_lcd_panel_invert_color(panel, true);
        esp_lcd_panel_swap_xy(panel, DISPLAY_SWAP_XY);
        esp_lcd_panel_mirror(panel, DISPLAY_MIRROR_X, DISPLAY_MIRROR_Y);
        
        display_ = new LcdDisplay(panel_io, panel, DISPLAY_BACKLIGHT_PIN, DISPLAY_BACKLIGHT_OUTPUT_INVERT,
                                    DISPLAY_WIDTH, DISPLAY_HEIGHT, DISPLAY_OFFSET_X, DISPLAY_OFFSET_Y, DISPLAY_MIRROR_X, DISPLAY_MIRROR_Y, DISPLAY_SWAP_XY);

        
    }
    
    // 物联网初始化，添加对 AI 可见设备
    void InitializeIot() {
        auto& thing_manager = iot::ThingManager::GetInstance();
        thing_manager.AddThing(iot::CreateThing("Speaker"));
    }

   

public:
    adc_oneshot_unit_handle_t adc_handle;
    LichuangDevBoard() : boot_button_(BOOT_BUTTON_GPIO) {
        InitializeI2c();
        InitializeSpi();
        InitializeSt7789Display();
        InitializeButtons();
        InitializeIot();

       
        adc_oneshot_unit_init_cfg_t init_config = {
            .unit_id = ADC_UNIT_1,
        };
        adc_oneshot_new_unit(&init_config, &adc_handle);

        adc_oneshot_chan_cfg_t config = {
            .atten = ADC_ATTEN_DB_11,
            .bitwidth = ADC_BITWIDTH_12,
        };
        adc_oneshot_config_channel(adc_handle, ADC_CHANNEL_9, &config);
        adc_oneshot_config_channel(adc_handle, ADC_CHANNEL_8, &config);
        // 读取 ADC 值
        // while (1) {
        //   int adc_raw_value = 0;
        //   adc_oneshot_read(adc_handle, ADC_CHANNEL_0, &adc_raw_value);

        //   ESP_LOGW(TAG, "ADC Raw Value: %d\n", adc_raw_value);

        //   // 延时 1 秒
        //   vTaskDelay(pdMS_TO_TICKS(1000));
        // }
    }
    double GetBattary() {
      int adc_raw_value = 0;
      int ref= 0;
      adc_oneshot_read(adc_handle, ADC_CHANNEL_8, &ref);
    //   ESP_LOGW("BAT", "battery voltage:%d" ,ref);  
      adc_oneshot_read(adc_handle, ADC_CHANNEL_9, &adc_raw_value);
    //   ESP_LOGW("BAT", "battery voltage:%d" ,adc_raw_value);  
      return  4.98f*adc_raw_value/ref;
    }
    virtual AudioCodec* GetAudioCodec() override {
        static BoxAudioCodec* audio_codec = nullptr;
        if (audio_codec == nullptr) {
            audio_codec = new BoxAudioCodec(i2c_bus_, AUDIO_INPUT_SAMPLE_RATE, AUDIO_OUTPUT_SAMPLE_RATE,
                AUDIO_I2S_GPIO_MCLK, AUDIO_I2S_GPIO_BCLK, AUDIO_I2S_GPIO_WS, AUDIO_I2S_GPIO_DOUT, AUDIO_I2S_GPIO_DIN,
                GPIO_NUM_NC, AUDIO_CODEC_ES8311_ADDR, AUDIO_CODEC_ES7210_ADDR, AUDIO_INPUT_REFERENCE);
            audio_codec->SetOutputVolume(AUDIO_DEFAULT_OUTPUT_VOLUME);
        }
        return audio_codec;
    }

    virtual I2cDevice* GetI2cDevice() override {
      static I2cDevice* I2c_codec = nullptr;
      I2c_codec = new  I2cDevice(i2c_bus_, qmi8658_device_addr);
      return I2c_codec;
    }

    ///改屏幕 注释
    virtual Display* GetDisplay() override {
        return display_;
    }
};


/// ******** F-5 month 23 day ***** UART 433 INIT  *********
// void UART_433_Init(uint32_t baudrate){

//     ESP_LOGW("BAT", "==== UART 433 BEGIN INIT ===");

//     uart_config_t uart_config;                          /* 串口配置句柄 */
 
//     uart_config.baud_rate = baudrate;                   /* 波特率 */
//     uart_config.data_bits = UART_DATA_8_BITS;           /* 字长为8位数据格式 */
//     uart_config.parity = UART_PARITY_DISABLE;           /* 无奇偶校验位 */
//     uart_config.stop_bits = UART_STOP_BITS_1;           /* 一个停止位 */
//     uart_config.flow_ctrl = UART_HW_FLOWCTRL_DISABLE;   /* 无硬件控制流 */
//     uart_config.source_clk = UART_SCLK_APB;             /* 配置时钟源 */
//     uart_config.rx_flow_ctrl_thresh = 122;              /* 硬件控制流阈值 */
    
//     uart_param_config(UART_433_PIN_NUM, &uart_config);          /* 配置uart端口 */
 
//     /* 配置  uart引脚 */
//     uart_set_pin(UART_433_PIN_NUM , UART_433_TX_PIN, UART_433_RX_PIN, -1, -1);
    
//     /* 安装串口驱动 */
//     uart_driver_install(UART_433_PIN_NUM, UART_433_RX_Buffer_Size * 2, UART_433_RX_Buffer_Size * 2, 20, NULL, 0);

//     ESP_LOGW("BAT", "==== UART 433 BEGIN HAVE INIT ===");
// }



// void USART_433_RX_DATA(){

//     uart_get_buffered_data_len(UART_433_PIN_NUM, (size_t*) &len); 
//     if(len>0)
//     {
//         memset(uart_433_data, 0, UART_433_RX_Buffer_Size); 
//         ESP_LOGW("BAT", "==== UART GET 01 ===");
//         uart_read_bytes(UART_433_PIN_NUM, uart_433_data, len, 100);
//         ESP_LOGW("BAT", "==== UART GET 02 ===");
//         uart_write_bytes(UART_433_PIN_NUM, (const char*)uart_433_data, strlen((const char*)uart_433_data));   /* 写数据 */
//         ESP_LOGW("BAT", "==== UART GET 03 ===");
//     }
    
// }

///*********************************************************** */
 

 
    
 








DECLARE_BOARD(LichuangDevBoard);
