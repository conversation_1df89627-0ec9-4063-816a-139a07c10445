#ifndef _BOARD_CONFIG_H_
#define _BOARD_CONFIG_H_

#include <driver/gpio.h>
// **********  F-5 month 22 day  --> include uart *********
#include "driver/uart.h"
#include "esp_log.h"
#include <string>
#include <functional>

#include <string.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "driver/uart.h"
#include "driver/uart_select.h"
#include "driver/gpio.h"


// **************************************************


#define AUDIO_INPUT_SAMPLE_RATE  16000
#define AUDIO_OUTPUT_SAMPLE_RATE 16000
#define AUDIO_DEFAULT_OUTPUT_VOLUME 80

#define AUDIO_INPUT_REFERENCE    true

#define AUDIO_I2S_GPIO_MCLK GPIO_NUM_38
#define AUDIO_I2S_GPIO_WS GPIO_NUM_13
#define AUDIO_I2S_GPIO_BCLK GPIO_NUM_14
#define AUDIO_I2S_GPIO_DIN  GPIO_NUM_12
#define AUDIO_I2S_GPIO_DOUT GPIO_NUM_45

// Audio control GPIO pin
#define AUDIO_CONTROL_PIN GPIO_NUM_15



#define AUDIO_CODEC_I2C_SDA_PIN  GPIO_NUM_1
#define AUDIO_CODEC_I2C_SCL_PIN  GPIO_NUM_2
#define AUDIO_CODEC_ES8311_ADDR  ES8311_CODEC_DEFAULT_ADDR
#define AUDIO_CODEC_ES7210_ADDR  0x82

// #define BUILTIN_LED_GPIO        GPIO_NUM_48
#define BOOT_BUTTON_GPIO        GPIO_NUM_47
#define VOLUME_UP_BUTTON_GPIO   GPIO_NUM_NC
#define VOLUME_DOWN_BUTTON_GPIO GPIO_NUM_NC

// *************  F-5 month 22 day -setting uart gpio  *****************
// 引脚和串口定义
#define UART_433_PIN_NUM           UART_NUM_2
#define UART_433_TX_PIN            GPIO_NUM_6
#define UART_433_RX_PIN            GPIO_NUM_7 
// 串口接收相关定义
#define UART_433_TX_Buffer_Size    (1024)
#define UART_433_RX_Buffer_Size    (1024)

//Function declaration
void UART_433_Init(uint32_t baudrate); /* 初始化串口 */
void UART_433_RX_DATA();


// ************************************************************

#define DISPLAY_WIDTH   320
#define DISPLAY_HEIGHT  240
#define DISPLAY_MIRROR_X true
#define DISPLAY_MIRROR_Y false
#define DISPLAY_SWAP_XY true

#define DISPLAY_OFFSET_X  0
#define DISPLAY_OFFSET_Y  0

#define DISPLAY_BACKLIGHT_PIN GPIO_NUM_42
#define DISPLAY_BACKLIGHT_OUTPUT_INVERT true

// set GPIO_NUM_4 as output high level
#define GPIO_OUTPUT_PIN_SEL (1ULL << GPIO_NUM_4)
#define GPIO_OUTPUT_LEVEL 1

#endif // _BOARD_CONFIG_H_
