#include <map>
#include <string>
#include <vector>
#include <arpa/inet.h>
#include "audio_decode_queue.h"

//全局宏定义管理
#include "project_config.h"

#include "protocol.h"
namespace {
#define AUDIO_OUTPUT_READY_EVENT (1 << 2)
#define CIRCAL_PLAY_LOCAL_AUDIO_OUTPUT_READY_EVENT (1 << 3)
static const char* TAG = "audio_decode_queue";


// 一个更简单、更安全的音频加速函数
std::vector<int16_t> ApplySpeedUp(const std::vector<int16_t>& pcm,
                                               float speed_factor) {
  // 基本安全检查
  if (pcm.empty() || speed_factor <= 1.0f) {
    return pcm;  // 返回原始数据
  }

  // 使用整数计算代替浮点数，避免潜在的浮点问题
  int skip = static_cast<int>(speed_factor);
  if (skip < 1) skip = 1;  // 安全检查

  // 预分配目标大小
  size_t estimated_size = pcm.size() / skip + 1;
  std::vector<int16_t> result;
  result.reserve(estimated_size);

  // 简单的定步长采样
  for (size_t i = 0; i < pcm.size(); i += skip) {
    result.push_back(pcm[i]);
  }

  return result;
}

}  // namespace

AudioDecodeQueue::AudioDecodeQueue(AudioCodec* codec, std::function<bool()> f)
    : codec_(codec), stop_function_(f) {
  //
  codec_->EnableOutput(true);
  //
  event_group_ = xEventGroupCreate();
  int opus_decode_sample_rate = 16000;  // 16000
  opus_decoder_ =
      std::make_unique<OpusDecoderWrapper>(opus_decode_sample_rate, 1);

    #if 1   ////  ----1号核运行---
    xTaskCreatePinnedToCore(
        [](void* arg) {
        AudioDecodeQueue* app = (AudioDecodeQueue*)arg;
        app->AudioProcess();
        vTaskDelete(NULL);
    },
    "AudioDecodeQueue", 4096 * 5, this, 8,  nullptr, 1);
   
      #else  
  xTaskCreate(
      [](void* arg) {
        AudioDecodeQueue* app = (AudioDecodeQueue*)arg;
        app->AudioProcess();
        vTaskDelete(NULL);
      },
      "AudioDecodeQueue", 4096 * 6, this, 2, nullptr); 
    #endif

    }
//
void AudioDecodeQueue::Stop() {
  xEventGroupClearBits(event_group_, AUDIO_OUTPUT_READY_EVENT);
}

#if 0 

void AudioDecodeQueue::AudioProcess(void) {
   while (true) {
    auto bits = xEventGroupWaitBits(
        event_group_,
        AUDIO_OUTPUT_READY_EVENT | CIRCAL_PLAY_LOCAL_AUDIO_OUTPUT_READY_EVENT,
        pdTRUE, pdFALSE, portMAX_DELAY);

    if (bits & AUDIO_OUTPUT_READY_EVENT) {
      // 只有TTS模式下才预缓冲
      if (queue_mode_ == AudioQueueMode::TTS && tts_waiting_for_buffer_) {
        std::lock_guard<std::mutex> lock(mutex_);
        if (audio_decode_queue_.size() < 12) { 
          //ESP_LOGW("aduio","----------------------- decode_queue < 12 ----------------------");
          vTaskDelay(pdMS_TO_TICKS(2));
          continue;
        } else {
          //ESP_LOGW("aduio","----------------------- decode_queue > 12 ----------------------");
          tts_waiting_for_buffer_ = false;
        }
      }
      if (OutputAudioSelect(audio_decode_queue_)) {
        xEventGroupSetBits(event_group_, AUDIO_OUTPUT_READY_EVENT);
      }
    }
    //本地音频
    if (bits & CIRCAL_PLAY_LOCAL_AUDIO_OUTPUT_READY_EVENT) {
      std::vector<std::vector<uint8_t>> data ;
      {
        std::lock_guard<std::mutex> lock(mutex_);
        if (loop_local_audio_decode_queue_.empty()) continue;
        data = loop_local_audio_decode_queue_;
      }
      OutputAudio(data);
      xEventGroupSetBits(event_group_,
                         CIRCAL_PLAY_LOCAL_AUDIO_OUTPUT_READY_EVENT);
      continue;
    }
  }
}

#else 
//
void AudioDecodeQueue::AudioProcess(void) {
  while (true) {
    auto bits = xEventGroupWaitBits(
        event_group_,
        AUDIO_OUTPUT_READY_EVENT | CIRCAL_PLAY_LOCAL_AUDIO_OUTPUT_READY_EVENT,
        pdTRUE, pdFALSE, portMAX_DELAY);
    //
    if (bits & CIRCAL_PLAY_LOCAL_AUDIO_OUTPUT_READY_EVENT) {
      std::vector<std::vector<uint8_t>> data ;
      {
        std::lock_guard<std::mutex> lock(mutex_);
        if (loop_local_audio_decode_queue_.empty()) continue;
        data = loop_local_audio_decode_queue_;
      }
      OutputAudio(data);
      xEventGroupSetBits(event_group_,
                         CIRCAL_PLAY_LOCAL_AUDIO_OUTPUT_READY_EVENT);
      continue;
    }
    if (bits & AUDIO_OUTPUT_READY_EVENT) {
      if (OutputAudioSelect(audio_decode_queue_)) {
        xEventGroupSetBits(event_group_, AUDIO_OUTPUT_READY_EVENT);
      }
    }
  }
}

#endif


//

void AudioDecodeQueue::CirclePlayData() {
  if (circal_play_local_audio_output_ready_envet_) {
    PlayLocalFile(circal_play_local_data_, circal_play_local_data_size_);
  }
}
//
bool AudioDecodeQueue::OutputAudioSelect(
     std::deque<std::vector<uint8_t>>& audio_decode_queue) {
  std::vector<std::vector<uint8_t>> data;
  int audio_decode_queue_size = 0;
  {
    
    std::lock_guard<std::mutex> lock(mutex_);
    audio_decode_queue_size = audio_decode_queue.size();
    data = FramesTodecode(audio_decode_queue);
  }
  if (audio_decode_queue_size == 0) {
    if (stop_function_()) {
      // codec->EnableOutput(false);
      return false;
    }
  }
  if (data.empty()) return  false;
  OutputAudio(data);
  if (audio_decode_queue_size > data.size()) {
    return true;
  }
  return true;
}
std::vector<std::vector<uint8_t>> AudioDecodeQueue::FramesTodecode(
    std::deque<std::vector<uint8_t>>& audio_decode_queue) {
  // std::lock_guard<std::mutex> lock(mutex_);
  // 队列大小监控和日志
  const size_t current_queue_size = audio_decode_queue.size();
  //
  if (current_queue_size == 0) return{};
  //
  // 基于队列大小动态确定播放速度
  speed_factor_ = 1.0f;
  // 队列过大时进行紧急处理
  constexpr size_t QUEUE_WARNING_THRESHOLD = 200;
  constexpr size_t QUEUE_CRITICAL_THRESHOLD = 500;

  if (current_queue_size > QUEUE_WARNING_THRESHOLD) {
    // ESP_LOGW(TAG, "Large audio queue: %zu frames", current_queue_size);

    // 根据队列大小动态调整播放速度
    if (current_queue_size > QUEUE_CRITICAL_THRESHOLD) {
      speed_factor_ = 1.9f;  // 队列严重过大时，使用2倍速
      ESP_LOGW(TAG,
               "Audio queue critically large (%zu frames), using 2x playback "
               "speed",
               current_queue_size);
    } else {
      // 在警告阈值和严重阈值之间线性调整速度: 1.2倍到1.8倍
      float range_position =
          static_cast<float>(current_queue_size - QUEUE_WARNING_THRESHOLD) /
          (QUEUE_CRITICAL_THRESHOLD - QUEUE_WARNING_THRESHOLD);
          speed_factor_ = 1.2f + range_position * 0.6f;  // 从1.2倍到1.8倍
      // ESP_LOGI(TAG, "Queue size large, applying speed factor: %.2f",
      //   speed_factor_);
    }
  }

  // 增加每次处理的帧数以加快队列消耗
  constexpr size_t MAX_FRAMES_PER_CALL = 10;  // 增加到10帧(原为3帧)

  // 根据队列大小动态调整处理帧数
  size_t frames_to_process = std::min(current_queue_size, MAX_FRAMES_PER_CALL);

  // 队列较大时处理更多帧
  if (current_queue_size > 100) {
    frames_to_process = std::min(current_queue_size, MAX_FRAMES_PER_CALL * 2);
  }

  if (frames_to_process > 0) {
    // 取出要处理的帧
    std::vector<std::vector<uint8_t>> frames_to_decode;
    frames_to_decode.reserve(frames_to_process);

    for (size_t i = 0; i < frames_to_process; i++) {
      frames_to_decode.push_back(std::move(audio_decode_queue.front()));
      audio_decode_queue.pop_front();
    }
    return frames_to_decode;
  }
  return {};
}

//
void AudioDecodeQueue::OutputAudio(
    const std::vector<std::vector<uint8_t>>& frames_to_decode) {
  // 预分配PCM缓冲区，避免频繁内存分配
  //
  if( frames_to_decode.empty())return;
  auto& codec = codec_;
  std::vector<int16_t> pcm_buffer;
  pcm_buffer.reserve(1024 * 8);  // 预分配足够大的缓冲区
    
  // 只在队列大于警告阈值时应用加速
  if (speed_factor_ != 1.0f) {
    // 使用固定的、安全的加速因子 - 不要尝试动态调整
    float speed_factor = speed_factor_;  // 固定使用1.5倍速

    ESP_LOGI(TAG, "Queue size large (%f frames), applying fixed 1.5x speed",
      speed_factor);

    // 批量解码
    for (auto& opus : frames_to_decode) {
      pcm_buffer.clear();
      if (opus_decoder_->Decode(opus, pcm_buffer)) {
        if (!pcm_buffer.empty()) {
          try {
            std::vector<int16_t> speed_up_pcm =
                ApplySpeedUp(pcm_buffer, speed_factor);
            // codec->EnableOutput(true);
            codec->OutputData(speed_up_pcm);
          } catch (...) {
            // 如果加速处理出错，回退到原始音频
            ESP_LOGE(TAG, "Error in audio acceleration, using original audio");
            // codec->EnableOutput(true);
            codec->OutputData(pcm_buffer);
          }
        }
      }
    }
  } else {
    // 队列正常大小，使用正常播放速度
    for (auto& opus : frames_to_decode) {
      pcm_buffer.clear();
      if (opus_decoder_->Decode(opus, pcm_buffer)) {
        // codec->EnableOutput(true);
        codec->OutputData(pcm_buffer);
      }
    }
  }
}

#if 1 

void AudioDecodeQueue::push_back(std::vector<uint8_t>&& data) {

  #if AUDIO_RECEIVE_DEBUG == 1
  ESP_LOGI(TAG, "*************Received audio data from cloud, size=%d bytes***************", data.size());
  #endif

    std::lock_guard<std::mutex> lock(mutex_);
    segment_buffer_.push_back(std::move(data));
    if (first_segment_) {
        // 首包立即flush
        while (!segment_buffer_.empty()) {
            audio_decode_queue_.push_back(std::move(segment_buffer_.front()));
            segment_buffer_.pop_front();
        }
        xEventGroupSetBits(event_group_, AUDIO_OUTPUT_READY_EVENT);
        first_segment_ = false;
        return;
    }
       //ESP_LOGE("audio_buffer","=================== audio_buffer buffer size :%u ====================",segment_buffer_.size());
    if (segment_buffer_.size() >= kSegmentFrameCount) {
      //ESP_LOGE("audio_buffer","============>>>>>>>>> audio_buffer buffer size :%u <<<<<<<<<<<==============",segment_buffer_.size());
        while (!segment_buffer_.empty()) {
            audio_decode_queue_.push_back(std::move(segment_buffer_.front()));
            segment_buffer_.pop_front();
        }
        xEventGroupSetBits(event_group_, AUDIO_OUTPUT_READY_EVENT);
    }
}

#else 
//
////接收来自云端的音频数据
void AudioDecodeQueue::push_back(std::vector<uint8_t>&& data) {
  //
  //
  #if AUDIO_RECEIVE_DEBUG == 1
  ESP_LOGI(TAG, "*************Received audio data from cloud, size=%d bytes***************", data.size());
  #endif

  std::lock_guard<std::mutex> lock(mutex_);
  audio_decode_queue_.push_back(std::move(data));
  xEventGroupSetBits(event_group_, AUDIO_OUTPUT_READY_EVENT);
}
#endif

void AudioDecodeQueue::FlushSegmentBuffer() {
  std::lock_guard<std::mutex> lock(mutex_);
  if (!segment_buffer_.empty()) {
    while (!segment_buffer_.empty()) {
      audio_decode_queue_.push_back(std::move(segment_buffer_.front()));
      segment_buffer_.pop_front();
    }
    xEventGroupSetBits(event_group_, AUDIO_OUTPUT_READY_EVENT);
  }
}



//
void AudioDecodeQueue::PlayLocalFile(const char* data, size_t size) {
  queue_mode_ = AudioQueueMode::Local;

  // 检查是否为Ogg封装格式 (头部是'OggS')
  //
  if (size >= 4 && data[0] == 'O' && data[1] == 'g' && data[2] == 'g' &&
      data[3] == 'S') {
    ESP_LOGI(TAG, "Detected Ogg container format, extracting Opus packets...");

    // 为了简单，我们将Ogg文件视为一系列数据包
    // 实际上，这需要更复杂的Ogg解析
    bool found_opus_header = false;

    // 搜索"OpusHead"标记，确认为Opus数据
    for (size_t i = 0; i < size - 8; i++) {
      if (memcmp(data + i, "OpusHead", 8) == 0) {
        found_opus_header = true;
        ESP_LOGI(TAG, "Found OpusHead marker at offset %d", i);
        break;
      }
    }

    if (!found_opus_header) {
      ESP_LOGE(TAG, "Ogg file does not contain Opus data");
      return;
    }

    // 设置为播放模式
    // SetDeviceState(kDeviceStateSpeaking);
    // SetDecodeSampleRate(16000);

    // 直接将整个Ogg文件发送到解码器
    // 这依赖于解码器能够处理Ogg容器
    std::vector<uint8_t> opus(data, data + size);

    std::lock_guard<std::mutex> lock(mutex_);
    audio_decode_queue_.push_back(std::move(opus));
    xEventGroupSetBits(event_group_, AUDIO_OUTPUT_READY_EVENT);
    return;
  }
  for (const char* p = data; p < data + size;) {
    auto p3 = (BinaryProtocol3*)p;
    p += sizeof(BinaryProtocol3);

    auto payload_size = ntohs(p3->payload_size);
    std::vector<uint8_t> opus;
    opus.resize(payload_size);
    memcpy(opus.data(), p3->payload, payload_size);
    p += payload_size;
    std::lock_guard<std::mutex> lock(mutex_);
    audio_decode_queue_.emplace_back(std::move(opus));
  }
  xEventGroupSetBits(event_group_, AUDIO_OUTPUT_READY_EVENT);
}

void AudioDecodeQueue::LoopPlayLocalFile(const char* data, size_t size) {
  queue_mode_ = AudioQueueMode::Local;

  circal_play_local_audio_output_ready_envet_ = true;
  circal_play_local_data_ = const_cast<char*>(data);
  circal_play_local_data_size_ = size;
  PlayLocalFile(circal_play_local_data_, circal_play_local_data_size_);
  // ESP_LOGI(TAG, "PlayLocalFile: %zu bytes", size);
  // {
  //   std::lock_guard<std::mutex> lock(mutex_);
  //   loop_local_audio_decode_queue_.clear();
  //   audio_decode_queue_.clear();
  // }
  // std::deque<std::vector<uint8_t>> audio_decode_queue;
  // // SetDecodeSampleRate(16000);
  // for (const char* p = data; p < data + size;) {
  //   auto p3 = (BinaryProtocol3*)p;
  //   p += sizeof(BinaryProtocol3);

  //   auto payload_size = ntohs(p3->payload_size);
  //   std::vector<uint8_t> opus;
  //   opus.resize(payload_size);
  //   memcpy(opus.data(), p3->payload, payload_size);
  //   p += payload_size;
  //   std::lock_guard<std::mutex> lock(mutex_);
  //   audio_decode_queue.emplace_back(std::move(opus));
  // }
  // while (!audio_decode_queue.empty()) {
  //   auto data = FramesTodecode(audio_decode_queue);
  //   loop_local_audio_decode_queue_.insert(loop_local_audio_decode_queue_.end(),
  //                                         data.begin(), data.end());
  // }
 
  // xEventGroupSetBits(event_group_, CIRCAL_PLAY_LOCAL_AUDIO_OUTPUT_READY_EVENT);
}