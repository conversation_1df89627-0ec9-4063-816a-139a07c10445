#ifndef __LOCAL_MOAN_H__
#define __LOCAL_MOAN_H__

#include <string>
#include <map>

extern const char p3_moan_1000020_start[] asm("_binary_moan_1000020_p3_start");
extern const char p3_moan_1000020_end[]   asm("_binary_moan_1000020_p3_end");
extern const char p3_moan_1000021_start[] asm("_binary_moan_1000021_p3_start");
extern const char p3_moan_1000021_end[]   asm("_binary_moan_1000021_p3_end");
extern const char p3_moan_1000022_start[] asm("_binary_moan_1000022_p3_start");
extern const char p3_moan_1000022_end[]   asm("_binary_moan_1000022_p3_end");
extern const char p3_moan_1000023_start[] asm("_binary_moan_1000023_p3_start");
extern const char p3_moan_1000023_end[]   asm("_binary_moan_1000023_p3_end");
extern const char p3_moan_1000024_start[] asm("_binary_moan_1000024_p3_start");
extern const char p3_moan_1000024_end[]   asm("_binary_moan_1000024_p3_end");
extern const char p3_moan_1000025_start[] asm("_binary_moan_1000025_p3_start");
extern const char p3_moan_1000025_end[]   asm("_binary_moan_1000025_p3_end");
extern const char p3_moan_1000026_start[] asm("_binary_moan_1000026_p3_start");
extern const char p3_moan_1000026_end[]   asm("_binary_moan_1000026_p3_end");
extern const char p3_moan_1000027_start[] asm("_binary_moan_1000027_p3_start");
extern const char p3_moan_1000027_end[]   asm("_binary_moan_1000027_p3_end");
extern const char p3_moan_1000028_start[] asm("_binary_moan_1000028_p3_start");
extern const char p3_moan_1000028_end[]   asm("_binary_moan_1000028_p3_end");
extern const char p3_moan_1000029_start[] asm("_binary_moan_1000029_p3_start");
extern const char p3_moan_1000029_end[]   asm("_binary_moan_1000029_p3_end");
extern const char p3_moan_1000030_start[] asm("_binary_moan_1000030_p3_start");
extern const char p3_moan_1000030_end[]   asm("_binary_moan_1000030_p3_end");
extern const char p3_moan_1000031_start[] asm("_binary_moan_1000031_p3_start");
extern const char p3_moan_1000031_end[]   asm("_binary_moan_1000031_p3_end");


#endif