#ifndef MQTT_PROTOCOL_H
#define MQTT_PROTOCOL_H

#include "protocol.h"
#include <mqtt.h>
#include <udp.h>
#include <cJSON.h>
#include <mbedtls/aes.h>
#include <freertos/FreeRTOS.h>
#include <freertos/event_groups.h>
#include <nvs_flash.h>
#include <nvs.h>
#include <string>
#include <vector>
#include <functional>
#include <mutex>

#define MQTT_PING_INTERVAL_SECONDS 90
#define MQTT_RECONNECT_INTERVAL_MS 10000
#define MQTT_PROTOCOL_SERVER_HELLO_EVENT (1 << 0)

class MqttProtocol : public Protocol {
public:
    MqttProtocol();
    ~MqttProtocol();

    void SendAudio(const std::vector<uint8_t>& data) override;
    bool OpenAudioChannel() override;
    void CloseAudioChannel() override;
    bool IsAudioChannelOpened() const override;

    void SetOnIncomingAudio(std::function<void(std::vector<uint8_t>&&)> callback) {
        on_incoming_audio_ = std::move(callback);
    }
    void SendCancelTTS(bool f=false );
    void SendImuStatesAndValue( const t_sQMI8658& imu_data,
        int touch_value);
    // 获取音量控制值并重置标志
    bool GetVolumeControl(std::string& value) {
        if (has_volume_control_) {
            value = volume_control_value_;
            has_volume_control_ = false;
            return true;
        }
        return false;
    }
    void UpdateLanguage(const std::string& language);
    void WakeupCall();
    bool StartMqttClient();

    #if WIFI_SIGNAL_CHECK_TONE == 1
    void OnConnected(std::function<void()> cb) override { on_connected_ = std::move(cb); }
    bool IsMqttConnected() const override { return mqtt_ && mqtt_->IsConnected(); }
    #endif

private:
    EventGroupHandle_t event_group_handle_;

    std::string endpoint_;
    std::string client_id_;
    std::string username_;
    std::string password_;
    std::string subscribe_topic_;
    std::string publish_topic_;
    std::string languagesType_;  // 用于保存 languagesType 值
    std::string user_id3_;
    std::mutex channel_mutex_;
    Mqtt* mqtt_ = nullptr;
    Udp* udp_ = nullptr;
    mbedtls_aes_context aes_ctx_;
    std::string aes_nonce_;
    std::string udp_server_;
    int udp_port_;
    uint32_t local_sequence_;
    uint32_t remote_sequence_;

    std::function<void(std::vector<uint8_t>&&)> on_incoming_audio_;

// 音量控制相关
    std::string volume_control_value_;
    bool has_volume_control_ = false;
    
    // 关机控制
    bool shutdown_requested_ = false;

    void ParseServerHello(const cJSON* root);
    std::string DecodeHexString(const std::string& hex_string);

    void SendText(const std::string& text) override;

    // 从 NVS 读取语言类型
    std::string LoadLanguageTypeFromNVS();
    
    #if WIFI_SIGNAL_CHECK_TONE == 1
    std::function<void()> on_connected_;
    #endif
    
};

#endif // MQTT_PROTOCOL_H
