#include "mqtt_protocol.h"
#include <esp_log.h>
#include <ml307_mqtt.h>
#include <esp_mqtt.h>
#include <mqtt_client.h>
#include <string.h>
#include <arpa/inet.h>
#include "board.h"
#include "application.h"
#include "settings.h"
#include <cstring>
#include "system_info.h"

//全局宏定义管理
#include "project_config.h"

#define TAG "MQTT"

MqttProtocol::MqttProtocol() {
    event_group_handle_ = xEventGroupCreate();

    // StartMqttClient();
}

MqttProtocol::~MqttProtocol() {
    ESP_LOGI(TAG, "MqttProtocol deinit");
    if (udp_ != nullptr) {
        delete udp_;
    }
    if (mqtt_ != nullptr) {
        delete mqtt_;
    }
    vEventGroupDelete(event_group_handle_);
}

bool MqttProtocol::StartMqttClient() {
    //Settings settings("mqtt", false);

    Settings settings("mqtt", true);  // 设置为读写模式

    endpoint_ = settings.GetString("endpoint");
    client_id_ = settings.GetString("client_id");
    username_ = settings.GetString("username");
    password_ = settings.GetString("password");
    subscribe_topic_ = settings.GetString("subscribe_topic");
   // publish_topic_ = settings.GetString("publish_topic");



    std::string user_id = SystemInfo::GetMacAddress()    ;  // 或者其他方式获取 userI
    std::string user_id2 = SystemInfo::GetMacAddressNoColon()    ;  // 或者其他方式获取 userI
    std::string user_id3 = SystemInfo::GetMacAddressDecimal()    ;  // 或者其他方式获取 userI

    ESP_LOGI(TAG, "Client **********: %s", user_id.c_str());  // 打印 client_id_
    ESP_LOGI(TAG, "Client **********: %s", user_id2.c_str());  // 打印 client_id_
    ESP_LOGI(TAG, "Client **********: %s", user_id3.c_str());  // 打印 client_id_

    std::string phone_control_topic = "doll/control/" + user_id3;
    std::string languagesType_topic = "doll/set/" + user_id3;
    std::string moan_topic = "doll/control_moan/" + user_id3;
    user_id3_ =  user_id3;
    
    // // 加载 NVS 中保存的语言类型
    std::string saved_language = LoadLanguageTypeFromNVS();
    if (!saved_language.empty()) {
        ESP_LOGI(TAG, "Loaded language type from NVS1233: %s", saved_language.c_str());
        // 在需要的地方使用 saved_language（例如：设置语言）
       // languagesType_ = saved_language;
    }

    
    publish_topic_ = "stt/doll/" + user_id3 + "/" + saved_language;



    if (mqtt_ != nullptr) {
        delete mqtt_;
        mqtt_ = nullptr;
    }

    // 移除 mqtt_cfg 配置，使用 sdkconfig 中的设置
    mqtt_ = Board::GetInstance().CreateMqtt();
    mqtt_->SetKeepAlive(90);

    mqtt_->OnDisconnected([this]() {
        ESP_LOGI(TAG, "Disconnected from endpoint");
    });

    // 设置消息回调
    mqtt_->OnMessage([this,languagesType_topic, phone_control_topic,moan_topic](const std::string& topic, const std::string& payload) {
        // 只在 Debug 级别打印详细日志
        ESP_LOGD(TAG, "MQTT Message: topic=%s, size=%d", topic.c_str(), payload.size());
        
        // 处理不同的主题
        if (topic == subscribe_topic_) {
            // 处理主语音通信主题
            // 判断是否是 JSON 消息
            if (payload[0] == '{') {
                ESP_LOGI(TAG, "Received JSON message: %s", payload.c_str());
                // JSON 消息交给 OnIncomingJson 处理
                if (on_incoming_json_ != nullptr) {
                    cJSON* root = cJSON_Parse(payload.c_str());
                    if (root != nullptr) {
                        on_incoming_json_(root);
                        cJSON_Delete(root);
                    }
                }
            } else {
                // PCM 数据交给 OnIncomingAudio 处理
                if (on_incoming_audio_ != nullptr) {
                    std::vector<uint8_t> audio_data(payload.begin(), payload.end());
                    on_incoming_audio_(std::move(audio_data));
                }
            }
        } 
        else if (topic == phone_control_topic) {
            // 处理控制消息主题
            ESP_LOGI(TAG, "Received control message: %s", payload.c_str());
            
            // 解析控制消息JSON
            cJSON* root = cJSON_Parse(payload.c_str());
            if (root != nullptr) {
                // 直接传给应用程序处理
                if (on_incoming_json_ != nullptr) {
                    on_incoming_json_(root);
                }
                cJSON_Delete(root);
            } else {
                ESP_LOGE(TAG, "Failed to parse control message JSON");
            }
        }
        else if (topic == languagesType_topic) {
            // 处理语言设置消息
            ESP_LOGI(TAG, "Received language setting: %s", payload.c_str());
            // 处理语言设置逻辑...
            cJSON* root = cJSON_Parse(payload.c_str());
            if (root != nullptr) {
                if (on_incoming_json_ != nullptr) {
                    on_incoming_json_(root);
                }
                cJSON_Delete(root);
            }
        } else if (topic == moan_topic) {
          // 处理语言设置消息
          ESP_LOGI(TAG, "Received moan: %s", payload.c_str());
          // 处理语言设置逻辑...
          cJSON* root = cJSON_Parse(payload.c_str());
          if (root != nullptr) {
            if (on_incoming_json_ != nullptr) {
              on_incoming_json_(root);
            }
            cJSON_Delete(root);
          }
        } else {
          ESP_LOGW(TAG, "Unhandled topic: %s", topic.c_str());
        }
    });

    // 在连接前添加此日志检查配置
    ESP_LOGI(TAG, "MQTT配置: endpoint='%s', client_id='%s', username='%s'",
             endpoint_.c_str(), client_id_.c_str(), username_.c_str());

    // 尝试连接
    ESP_LOGI(TAG, "Connecting to MQTT broker: %s", endpoint_.c_str());
    if (!mqtt_->Connect(endpoint_, MWTT_PORT, client_id_, username_, password_)) {
        ESP_LOGE(TAG, "Failed to connect to MQTT broker");
        return false;
    }
    ESP_LOGI(TAG, "Connected to endpoint");

    #if WIFI_SIGNAL_CHECK_TONE == 1
    if (on_connected_) on_connected_();
    #endif

    if (!subscribe_topic_.empty()) {
        mqtt_->Subscribe(subscribe_topic_, 2);
        /*********************************************/ 
        ESP_LOGI(TAG, "Subscribing to topic: %s", subscribe_topic_.c_str());       
        mqtt_->Subscribe(phone_control_topic, 0); 
        ESP_LOGI(TAG, "phone_control_topic: %s", phone_control_topic.c_str());
        mqtt_->Subscribe(languagesType_topic, 0); 
        ESP_LOGI(TAG, "languagesType_topic: %s", languagesType_topic.c_str());
        mqtt_->Subscribe(moan_topic, 0); 
        ESP_LOGI(TAG, "moan_topic: %s", languagesType_topic.c_str());

    }

    esp_mqtt_client_config_t mqtt_cfg = {};
    mqtt_cfg.broker.address.uri = endpoint_.c_str();
    mqtt_cfg.broker.address.port = MWTT_PORT;
    mqtt_cfg.credentials.client_id = client_id_.c_str();
    mqtt_cfg.credentials.username = username_.c_str();
    mqtt_cfg.credentials.authentication.password = password_.c_str();
    
    // 增加缓冲区大小
    mqtt_cfg.buffer.size = 8192;        // 接收缓冲区大小
    mqtt_cfg.buffer.out_size = 4096;    // 发送缓冲区大小
    mqtt_cfg.network.disable_auto_reconnect = false;  
    mqtt_cfg.session.message_retransmit_timeout = 30;
    mqtt_cfg.network.timeout_ms = 10000;

    return true;
}



/*********************************************/ 

// Add new method to update language
void MqttProtocol::UpdateLanguage(const std::string& language) {
    languagesType_ = language;
    
    // Update the publish topic with the new language
    std::string user_id = SystemInfo::GetMacAddressDecimal();
    publish_topic_ = "stt/doll/" + user_id + "/" + language;
    
    ESP_LOGI(TAG, "Updated publish topic to: %s, language: %s", publish_topic_.c_str(), language.c_str());
}

void MqttProtocol::WakeupCall() {
    std::string user_id = SystemInfo::GetMacAddressDecimal();
    std::string wakeup_topic = "stt/audio/text";
    
    // Create JSON message according to protocol specification
    cJSON* root = cJSON_CreateObject();
    cJSON_AddStringToObject(root, "device_id", user_id.c_str());
    cJSON_AddStringToObject(root, "device_type", "doll"); 
    cJSON_AddStringToObject(root, "stt_text", "Device is ready#");
    cJSON_AddStringToObject(root, "modal_type", "audio");
    char* json_string = cJSON_PrintUnformatted(root);
    mqtt_->Publish(wakeup_topic, json_string);
    
    // Free allocated memory
    free(json_string);
    cJSON_Delete(root);
    
    ESP_LOGI(TAG, "Published wakeup call to %s", wakeup_topic.c_str());
}


std::string MqttProtocol::LoadLanguageTypeFromNVS() {
    std::string saved_language;
    nvs_handle_t nvs_handle;
    esp_err_t err = nvs_open("config", NVS_READWRITE, &nvs_handle);
    if (err == ESP_OK) {
        size_t required_size;
        err = nvs_get_str(nvs_handle, "languagesType", NULL, &required_size);
        if (err == ESP_OK) {
            char* lang = new char[required_size];
            err = nvs_get_str(nvs_handle, "languagesType", lang, &required_size);
            if (err == ESP_OK) {
                saved_language = std::string(lang);
                ESP_LOGI(TAG, "Loaded languagesType from NVS: %s", saved_language.c_str());
            } else {
                ESP_LOGE(TAG, "Error reading languagesType from NVS");
            }
            delete[] lang;
        } else {
            ESP_LOGW(TAG, "languagesType not found in NVS, defaulting to 'en'");
            saved_language = "zh";  // 默认语言
        }
        nvs_close(nvs_handle);
    } else {
        ESP_LOGE(TAG, "Failed to open NVS for reading");
    }
    return saved_language;
}


/*********************************************/ 









void MqttProtocol::SendText(const std::string& text) {
    if (publish_topic_.empty()) {
        return;
    }
    mqtt_->Publish(publish_topic_, text);
}

void MqttProtocol::SendAudio(const std::vector<uint8_t>& data) {
    #if AUDIO_UPLOAD_DEBUG == 1

        #if 1
        ESP_LOGI("AUDIO_UPLOAD", "+++++++++++ Uploading audio: size=%d, first_bytes=%02x %02x %02x %02x +++++++++", 
              data.size(), 
              data.size() > 0 ? data[0] : 0, 
              data.size() > 1 ? data[1] : 0, 
              data.size() > 2 ? data[2] : 0, 
              data.size() > 3 ? data[3] : 0);
        #else
        static uint32_t audio_frame_seq = 0; // 添加这一行
        ESP_LOGI(TAG, ">>>>> SendAudio: seq=%lu, size=%zu, first_bytes=%02x %02x %02x %02x <<<<<", 
            audio_frame_seq++, data.size(), 
            data.size() > 0 ? data[0] : 0, 
            data.size() > 1 ? data[1] : 0, 
            data.size() > 2 ? data[2] : 0, 
            data.size() > 3 ? data[3] : 0);
        #endif

    #endif

    if (mqtt_ == nullptr || !mqtt_->IsConnected()) {
        ESP_LOGE(TAG, "MQTT client not connected");
        return;
    }

    // 分片发送大的音频数据
    const size_t MAX_CHUNK_SIZE = 1024;  // 每片最大1KB
    size_t remaining = data.size();
    size_t offset = 0;
    
    while (remaining > 0) {
        size_t chunk_size = std::min(remaining, MAX_CHUNK_SIZE);
        std::string payload(reinterpret_cast<const char*>(data.data() + offset), chunk_size);
        bool result = mqtt_->Publish(publish_topic_, payload, 0);  // 使用QoS 0，无需确认
        
        if (!result) {
            ESP_LOGE(TAG, "Failed to publish audio chunk");
            return;
        }
        
        remaining -= chunk_size;
        offset += chunk_size;
    }

}

void MqttProtocol::SendImuStatesAndValue(const t_sQMI8658& imu_data,
                                         int touch_value) {
  if (mqtt_ == nullptr || !mqtt_->IsConnected()) {
    ESP_LOGE(TAG, "MQTT client not connected");
    return;
  }

  if (user_id3_.empty()) {
    ESP_LOGE(TAG, "User ID is empty");
    return;
  }

  // 构建JSON消息
  cJSON* root = cJSON_CreateObject();
  if (root == NULL) {
    ESP_LOGE(TAG, "Failed to create JSON object");
    return;
  }

  // 添加state字段
  int touch_value_temp = touch_value;
 
  cJSON_AddNumberToObject(root, "imu_type", imu_data.motion);
  cJSON_AddNumberToObject(root, "gx", imu_data.gyr_x);
  cJSON_AddNumberToObject(root, "gy", imu_data.gyr_y);
  cJSON_AddNumberToObject(root, "gz", imu_data.gyr_z);
  cJSON_AddNumberToObject(root, "ax", imu_data.acc_x);
  cJSON_AddNumberToObject(root, "ay", imu_data.acc_y);
  cJSON_AddNumberToObject(root, "az", imu_data.acc_z);
  cJSON_AddNumberToObject(root, "touch_value", touch_value_temp);
  // 添加device_id字段
  //
 
  cJSON_AddStringToObject(root, "device_id", user_id3_.c_str());

  // 将JSON转换为字符串
  char* message_str = cJSON_PrintUnformatted(root);
  if (message_str == NULL) {
    ESP_LOGE(TAG, "Failed to print JSON");
    cJSON_Delete(root);
    return;
  }

  std::string message(message_str);
  std::string imu_topic = "doll/imu_status";

  ESP_LOGI(TAG, "Sending IMU data: %s to topic: %s", message_str,
           imu_topic.c_str());

  // 发布消息
  mqtt_->Publish(imu_topic, message);

  // 清理资源
  cJSON_free(message_str);
  cJSON_Delete(root);
}

void MqttProtocol::CloseAudioChannel() {
    ESP_LOGI(TAG, "Sending end of audio stream");
    mqtt_->Publish(publish_topic_, "END", 1);

    if (on_audio_channel_closed_ != nullptr) {
        on_audio_channel_closed_();
    }
}

bool MqttProtocol::OpenAudioChannel() {
    if (mqtt_ == nullptr || !mqtt_->IsConnected()) {
        ESP_LOGI(TAG, "MQTT is not connected, try to connect now");
        if (!StartMqttClient()) {
            return false;
        }
    }

    if (on_audio_channel_opened_ != nullptr) {
        on_audio_channel_opened_();
    }
    return true;
}

void MqttProtocol::ParseServerHello(const cJSON* root) {
    auto transport = cJSON_GetObjectItem(root, "transport");
    if (transport == nullptr || strcmp(transport->valuestring, "udp") != 0) {
        ESP_LOGE(TAG, "Unsupported transport: %s", transport->valuestring);
        return;
    }

    auto session_id = cJSON_GetObjectItem(root, "session_id");
    if (session_id != nullptr) {
        session_id_ = session_id->valuestring;
    }

    auto audio_params = cJSON_GetObjectItem(root, "audio_params");
    if (audio_params != NULL) {
        auto sample_rate = cJSON_GetObjectItem(audio_params, "sample_rate");
        if (sample_rate != NULL) {
            server_sample_rate_ = sample_rate->valueint;
        }
    }

    auto udp = cJSON_GetObjectItem(root, "udp");
    if (udp == nullptr) {
        ESP_LOGE(TAG, "UDP is not specified");
        return;
    }
    udp_server_ = cJSON_GetObjectItem(udp, "server")->valuestring;
    udp_port_ = cJSON_GetObjectItem(udp, "port")->valueint;
    auto key = cJSON_GetObjectItem(udp, "key")->valuestring;
    auto nonce = cJSON_GetObjectItem(udp, "nonce")->valuestring;

    aes_nonce_ = DecodeHexString(nonce);
    mbedtls_aes_init(&aes_ctx_);
    mbedtls_aes_setkey_enc(&aes_ctx_, (const unsigned char*)DecodeHexString(key).c_str(), 128);
    local_sequence_ = 0;
    remote_sequence_ = 0;
    xEventGroupSetBits(event_group_handle_, MQTT_PROTOCOL_SERVER_HELLO_EVENT);
}

static const char hex_chars[] = "0123456789ABCDEF";
static inline uint8_t CharToHex(char c) {
    if (c >= '0' && c <= '9') return c - '0';
    if (c >= 'A' && c <= 'F') return c - 'A' + 10;
    if (c >= 'a' && c <= 'f') return c - 'a' + 10;
    return 0;
}

std::string MqttProtocol::DecodeHexString(const std::string& hex_string) {
    std::string decoded;
    decoded.reserve(hex_string.size() / 2);
    for (size_t i = 0; i < hex_string.size(); i += 2) {
        char byte = (CharToHex(hex_string[i]) << 4) | CharToHex(hex_string[i + 1]);
        decoded.push_back(byte);
    }
    return decoded;
}

bool MqttProtocol::IsAudioChannelOpened() const {
    return udp_ != nullptr;
}

void MqttProtocol::SendCancelTTS(bool f) {
    // 获取设备ID（十进制MAC地址）
    std::string device_id = SystemInfo::GetMacAddressDecimal();
    // 构造JSON消息
    std::string message;
    if (f) {
      message = "{\"user_id\":\"" + device_id + "\",\"action\":\"finish\"}";
    } else {
      message = "{\"user_id\":\"" + device_id + "\",\"action\":\"stop\"}";
    }

    // 打印日志
    ESP_LOGI(TAG, "Sending CancelTTS message: %s", message.c_str());
    
    // 发送到tts/cancel主题
    mqtt_->Publish("tts/cancel", message, 2);
    
    ESP_LOGI(TAG, "CancelTTS message sent to topic: tts/cancel");
}