#ifndef PROTOCOL_H
#define PROTOCOL_H

#include <cJSON.h>
#include <string>
#include <functional>
#include  "esp32_s3_szp.h"
//全局宏定义管理
#include "project_config.h"

struct BinaryProtocol3 {
    uint8_t type;
    uint8_t reserved;
    uint16_t payload_size;
    uint8_t payload[];
} __attribute__((packed));

#define MWTT_PORT  1883
enum AbortReason {
    kAbortReasonNone,
    kAbortReasonWakeWordDetected
};

enum ListeningMode {
    kListeningModeAutoStop,
    kListeningModeManualStop,
    kListeningModeAlwaysOn // 需要 AEC 支持
};

class Protocol {
public:
    virtual ~Protocol() = default;

    inline int server_sample_rate() const {
        return server_sample_rate_;
    }

    virtual void OnIncomingAudio(std::function<void(std::vector<uint8_t>&&)> callback);
    virtual void SetOnIncomingAudio(std::function<void(std::vector<uint8_t>&&)> callback) = 0;
    
    void OnIncomingJson(std::function<void(const cJSON* root)> callback);
    void OnAudioChannelOpened(std::function<void()> callback);
    void OnAudioChannelClosed(std::function<void()> callback);
    void OnNetworkError(std::function<void(const std::string& message)> callback);

    virtual void SendImuStatesAndValue(const t_sQMI8658& imu_data,
                                       int touch_value) = 0;
    virtual bool OpenAudioChannel() = 0;
    virtual void CloseAudioChannel() = 0;
    virtual bool IsAudioChannelOpened() const = 0;
    virtual void SendAudio(const std::vector<uint8_t>& data) = 0;
    virtual void SendWakeWordDetected(const std::string& wake_word);
    virtual void SendStartListening(ListeningMode mode);
    virtual void SendStopListening();
    virtual void SendAbortSpeaking(AbortReason reason);
    virtual void SendIotDescriptors(const std::string& descriptors);
    virtual void SendIotStates(const std::string& states);
    virtual void UpdateLanguage(const std::string& language) = 0;
    virtual void SendCancelTTS(bool f = false) = 0;
    virtual void WakeupCall() = 0;

    virtual bool StartMqttClient() = 0;


    #if WIFI_SIGNAL_CHECK_TONE == 1
    virtual bool IsMqttConnected() const { return false; }
    virtual void OnConnected(std::function<void()> cb) {}
    #endif

   protected:
    std::function<void(const cJSON* root)> on_incoming_json_;
    std::function<void(std::vector<uint8_t>&&)> on_incoming_audio_;
    std::function<void()> on_audio_channel_opened_;
    std::function<void()> on_audio_channel_closed_;
    std::function<void(const std::string& message)> on_network_error_;

    int server_sample_rate_ = 16000;
    std::string session_id_;

    virtual void SendText(const std::string& text) = 0;
    
};

#endif // PROTOCOL_H

