menu "<PERSON><PERSON> Assistant"

config OTA_VERSION_URL
    string "OTA Version URL"
    default "https://api.tenclass.net/xiaozhi/ota/"
    help
        The application will access this URL to check for updates.

choice CONNECTION_TYPE
    prompt "Connection Type"
    default CONNECTION_TYPE_MQTT_UDP
    help
        网络数据传输协议
    config CONNECTION_TYPE_MQTT_UDP
        bool "MQTT + UDP"
    config CONNECTION_TYPE_WEBSOCKET
        bool "Websocket"
endchoice

config WEBSOCKET_URL
    depends on CONNECTION_TYPE_WEBSOCKET
    string "Websocket URL"
    default "wss://api.tenclass.net/xiaozhi/v1/"
    help
        Communication with the server through websocket after wake up.

config WEBSOCKET_ACCESS_TOKEN
    depends on CONNECTION_TYPE_WEBSOCKET
    string "Websocket Access Token"
    default "test-token"
    help
        Access token for websocket communication.

choice BOARD_TYPE
    prompt "Board Type"
    default BOARD_TYPE_BREAD_COMPACT_WIFI
    help
        Board type. 开发板类型
    config BOARD_TYPE_BREAD_COMPACT_WIFI
        bool "面包板新版接线（WiFi）"
    config BOARD_TYPE_BREAD_COMPACT_ML307
        bool "面包板新版接线（ML307 AT）"
    config BOARD_TYPE_ESP_BOX_3
        bool "ESP BOX 3"
    config BOARD_TYPE_KEVIN_BOX_1
        bool "Kevin Box 1"
    config BOARD_TYPE_KEVIN_BOX_2
        bool "Kevin Box 2"
    config BOARD_TYPE_KEVIN_C3
        bool "Kevin C3"
    config BOARD_TYPE_LICHUANG_DEV
        bool "立创·实战派ESP32-S3开发板"
    config BOARD_TYPE_LICHUANG_C3_DEV
        bool "立创·实战派ESP32-C3开发板"
    config BOARD_TYPE_MAGICLICK_2P4
        bool "神奇按钮 Magiclick_2.4"  
    config BOARD_TYPE_M5STACK_CORE_S3
        bool "M5Stack CoreS3"
    config BOARD_TYPE_ATOMS3R_ECHO_BASE
        bool "AtomS3R + Echo Base"
    config BOARD_TYPE_XMINI_C3
        bool "虾哥 Mini C3"
    config BOARD_TYPE_ESP32S3_KORVO2_V3
        bool "ESP32S3_KORVO2_V3开发板"
    config BOARD_TYPE_ESP32S3_Touch_AMOLED_1_8
        bool "Waveshare ESP32-S3-Touch-AMOLED-1.8"
endchoice

endmenu
