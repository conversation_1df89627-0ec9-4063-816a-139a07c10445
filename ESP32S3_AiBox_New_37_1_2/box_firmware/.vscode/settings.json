{"idf.port": "/dev/ttyUSB0", "idf.openOcdConfigs": ["board/esp32s3-builtin.cfg"], "idf.customExtraVars": {"IDF_TARGET": "esp32s3"}, "idf.flashType": "UART", "files.associations": {"functional": "cpp"}, "idf.portWin": "COM3", "idf.espIdfPathWin": "d:\\ESP_SDK\\container\\v5.3.3\\esp-idf", "idf.toolsPathWin": "d:\\ESP_SDK\\tools", "idf.pythonInstallPath": "d:\\ESP_SDK\\tools\\tools\\idf-python\\3.11.2\\python.exe"}