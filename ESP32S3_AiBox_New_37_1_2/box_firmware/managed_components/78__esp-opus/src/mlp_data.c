/*This file is automatically generated from a Keras model*/

#ifdef HAVE_CONFIG_H
#include "config.h"
#endif

#include "mlp.h"

static const opus_int8 layer0_weights[800] = {
   -30, -9, 2, -12, 5, -1, 8, 9,
   9, 8, -13, 18, -17, -34, -5, 17,
   -11, 0, -4, 10, 2, 10, 15, -8,
   2, -1, 0, 5, 13, -3, -16, 1,
   -5, 3, 7, -28, -13, 6, 36, -3,
   19, -60, -17, -28, 7, -11, -30, -7,
   2, -42, -21, -3, 6, -22, 33, -9,
   7, -30, 21, -14, 24, -11, -20, -18,
   -5, -12, 12, -49, -50, -49, 16, 9,
   -37, -1, 9, 34, -13, -31, -31, 12,
   16, 44, -42, 2, -9, 8, -18, -6,
   9, 36, 19, 11, 13, 12, -21, 3,
   -28, -12, 3, 33, 25, -14, 11, 1,
   -94, -39, 18, -12, -11, -15, -7, 49,
   52, 10, -43, 9, 57, 8, 21, -6,
   14, -15, 44, -8, 7, -30, -13, -2,
   -9, 25, -2, -127, 18, -11, -52, 26,
   -27, 27, 10, -10, 7, 43, 6, -24,
   41, 10, -18, -27, 10, 17, 9, 10,
   -17, -10, 20, -6, 22, 55, 35, -80,
   36, 25, -24, -36, 15, 9, -19, 88,
   19, 64, -51, -35, 17, 0, -7, 41,
   -16, 27, 4, 15, -1, 18, -16, 47,
   -39, -54, -8, 13, -25, -20, 102, -18,
   -5, 44, 11, -28, 71, 2, -51, -5,
   5, 2, -83, -9, -29, 8, 21, -53,
   58, -37, -7, 13, 38, 9, 34, -1,
   -41, 21, 4, -24, -36, -33, -21, 32,
   75, -2, 1, -68, -1, 47, -29, 32,
   20, 12, -65, -87, 5, 16, -12, 24,
   40, 15, 7, 19, -26, -17, 17, 6,
   -2, -37, -30, -9, 32, -127, -39, 0,
   -31, -27, 4, -22, 23, -6, -77, 35,
   -61, 32, -37, -24, 13, -11, -1, -40,
   -3, 17, -7, 13, 11, 59, -19, 10,
   6, -18, 0, 13, 3, -6, -23, 19,
   11, -17, 13, -1, -80, 40, -53, 69,
   -29, -54, 0, -4, 33, -25, -2, 38,
   35, 36, -15, 46, 2, -13, -16, -8,
   -8, 12, -24, -9, -55, -5, -9, 32,
   11, 7, 12, -18, -10, -86, -38, 54,
   37, -25, 18, -43, 7, -27, -27, -54,
   13, 9, 22, 70, 6, 35, -7, 23,
   -15, -44, -6, 7, -66, -85, 32, 40,
   -19, -9, -7, 12, -15, 7, 2, 6,
   -35, 11, 28, 0, 26, 14, 1, 1,
   4, 12, 18, 35, 22, -18, -3, 14,
   -1, 7, 14, -8, -14, -3, 4, -3,
   -19, -7, -1, -25, -27, 25, -26, -2,
   33, -22, -27, -25, 4, -9, 7, 21,
   26, -30, 10, -9, -20, 11, 27, 10,
   5, -18, 14, -4, 2, -17, -5, -7,
   -9, -13, 15, 29, 1, -10, -16, -10,
   35, 36, -7, -22, -44, 17, 30, 22,
   21, -1, 22, -11, 32, -8, -7, 5,
   -10, 5, 30, -20, 29, -20, -34, 12,
   -4, -6, 6, -13, 10, -5, -68, -1,
   24, 9, 19, -24, -64, 31, 19, 27,
   -26, 75, -45, 41, 39, -42, 8, 6,
   23, -30, 16, -25, 30, 34, 8, -38,
   -3, 18, 16, -31, 22, -4, -9, 1,
   20, 9, 38, -32, 0, -45, 0, -6,
   -13, 11, -25, -32, -22, 31, -24, -11,
   -11, -4, -4, 20, -34, 22, 20, 9,
   -25, 27, -5, 28, -29, 29, 6, 21,
   -6, -18, 54, 4, -46, 23, 21, -14,
   -31, 36, -41, -24, 4, 22, 10, 11,
   7, 36, -32, -13, -52, -17, 24, 28,
   -37, -36, -1, 24, 9, -38, 35, 48,
   18, 2, -1, 45, 10, 39, 24, -38,
   13, 8, -16, 8, 25, 11, 7, -29,
   -11, 7, 20, -30, -38, -45, 14, -18,
   -28, -9, 65, 61, 22, -53, -38, -16,
   36, 46, 20, -39, 32, -61, -6, -6,
   -36, -33, -18, -28, 56, 101, 45, 11,
   -28, -23, -29, -61, 20, -47, 2, 48,
   27, -17, 1, 40, 1, 3, -51, 15,
   35, 28, 22, 35, 53, -61, -29, 12,
   -6, -21, 10, 3, -20, 2, -25, 1,
   -6, 31, 11, -3, 1, -10, -52, 6,
   126, -105, 122, 127, -128, 127, 127, -128,
   127, 108, 12, 127, 48, -128, -36, -128,
   127, 127, -128, -128, 127, 89, -128, 127,
   -128, -128, -128, 127, 127, -128, -128, -93,
   -82, 20, 125, 65, -82, 127, 38, -74,
   81, 88, -88, 79, 51, -47, -111, -26,
   14, 83, -88, -112, 24, 35, -101, 98,
   -99, -48, -45, 46, 83, -60, -79, 45,
   -20, -41, 9, 4, 52, 54, 93, -10,
   4, 13, 3, 123, 6, 94, -111, -69,
   -14, -31, 10, 12, 53, -79, -11, -21,
   -2, -44, -72, 92, 65, -57, 56, -38,
   127, -56, -128, 127, 127, -128, 86, 117,
   -75, -128, 127, -19, -99, -112, 127, -128,
   127, -48, 114, 118, -128, -128, 117, -17,
   -6, 121, -128, 127, -128, 82, 54, -106,
   127, 127, -33, 100, -39, -23, 18, -78,
   -34, -29, -1, -30, 127, -26, 127, -128,
   126, -128, 27, -23, -79, -120, -127, 127,
   72, 66, 29, 7, -66, -56, -117, -128
};

static const opus_int8 layer0_bias[32] = {
   51, -16, 1, 13, -5, -6, -16, -7,
   11, -6, 106, 26, 28, -14, 21, -29,
   7, 18, -18, -17, 21, -17, -9, 20,
   -25, -3, -34, 48, 11, -13, -31, -20
};

static const opus_int8 layer1_weights[2304] = {
   22, -1, -7, 7, 29, -27, -31, -17,
   -13, 33, 44, -8, 11, 33, 24, 78,
   15, 19, 30, -2, -24, 5, 49, 5,
   36, 29, -14, -11, -48, -33, 21, -42,
   -38, -12, 55, -37, 54, -8, 1, 36,
   17, 0, 51, 31, 59, 7, -12, 53,
   4, 32, -14, 48, 5, -10, -16, -8,
   1, -16, -56, -24, -6, 18, -2, 23,
   6, 46, -6, -10, 20, 35, -44, -15,
   -49, 36, 16, 5, -7, -79, -67, 12,
   70, -3, -79, -54, -85, -24, 47, -22,
   33, 21, 69, -1, 11, 22, 14, -16,
   -16, -22, -28, -11, 11, -41, 31, -26,
   -33, -19, -4, 27, 32, -50, 5, -10,
   -38, -22, -8, 35, -31, 1, -41, -15,
   -11, 44, 28, -17, -41, -23, 17, 2,
   -23, -26, -13, -13, -17, 6, 14, -31,
   -25, 9, -19, 39, -8, 4, 31, -1,
   -45, -11, -28, -92, -46, -15, 21, 118,
   -22, 45, -51, 11, -20, -20, -15, 13,
   -21, -97, -29, -32, -23, -42, 94, 1,
   23, -8, 63, -3, -46, 19, -26, 32,
   -40, -74, -26, 26, -4, -13, 30, -20,
   -30, -25, -14, -31, -45, -43, 4, -60,
   -48, -12, -34, 2, 2, 3, 13, 15,
   11, 16, 5, 46, -9, -55, -16, -57,
   29, 14, 38, -50, -2, -44, -11, -8,
   52, -27, -38, -7, 20, 47, 17, -59,
   0, 47, 46, -63, 35, -17, 19, 33,
   68, -19, 2, 15, -16, 28, -16, -103,
   26, -35, 47, -39, -60, 30, 31, -23,
   -52, -13, 116, 47, -25, 30, 40, 30,
   -22, 2, 12, -27, -18, 31, -10, 27,
   -8, -66, 12, 14, 4, -26, -28, -13,
   3, 13, -26, -51, 37, 5, 2, -21,
   47, 3, 13, 25, -41, -27, -8, -4,
   5, -76, -33, 28, 10, 9, -46, -74,
   19, 28, 25, 31, 54, -55, 68, 38,
   -24, -32, 2, 4, 68, 11, -1, 99,
   5, 16, -2, -74, 40, 26, -26, 33,
   31, -1, -68, 14, -6, 25, 9, 29,
   60, 61, 7, -7, 0, -24, 7, 77,
   4, -1, 16, -7, 13, -15, -19, 28,
   -31, -24, -16, 37, 24, 13, 30, 10,
   -30, 11, 11, -10, 22, 60, 28, 45,
   -3, -40, -62, -5, -102, 9, -32, -27,
   -54, 21, 15, -5, 37, -43, -11, 37,
   -19, 47, -64, -128, -27, -114, 21, -66,
   59, 46, -3, -12, -87, -9, 4, 19,
   -113, -36, 78, 57, -26, -38, -77, -10,
   6, 6, -75, 25, -97, -11, 33, -46,
   1, 13, -21, -33, -20, 16, -6, -3,
   -11, -4, -27, 38, 8, -41, -2, -33,
   18, 19, -26, 1, -29, -22, -4, -14,
   -55, -11, -80, -3, 11, 34, 90, 51,
   11, 17, 43, 36, 127, -32, 29, 103,
   9, 27, 13, 64, 56, 70, -14, 3,
   -12, 10, 37, 3, 12, -22, -10, 46,
   28, 10, 20, 26, -24, 18, 9, 7,
   14, 34, -5, -7, 31, -14, -56, 11,
   -18, -8, -17, -7, -10, -40, 10, -33,
   -32, -43, 5, 9, 11, -4, 10, 50,
   -12, -5, 46, 9, 7, 1, 11, 15,
   91, -17, 7, -50, 23, 6, -30, -99,
   0, -17, 14, 8, -10, -25, -30, -69,
   -62, 31, 127, 114, -23, 101, -5, -54,
   -6, -22, 7, -56, 39, 18, -29, 0,
   46, 8, -79, 4, -21, 18, -32, 62,
   -12, -8, -12, -58, 31, -32, 17, 6,
   -24, 25, 24, 9, -4, -19, 45, 6,
   17, -14, 5, -27, 16, -4, -41, 25,
   -36, 5, 15, 12, 50, 27, 25, 23,
   -44, -69, -9, -19, -48, -8, 4, 12,
   -6, 13, -19, -30, -36, 26, 37, -1,
   -3, -30, -42, -14, -10, -20, 26, -54,
   -27, -44, 4, 73, -26, 90, 32, -69,
   -29, -16, 3, 103, 15, -17, 37, 24,
   -23, -31, 33, -37, -64, 25, 13, -81,
   -28, -32, 27, 5, -35, -23, 15, -22,
   19, -7, 9, 30, 19, -23, 27, -13,
   43, 29, -29, -6, 9, -40, -33, -33,
   -32, 9, 11, -48, -8, -23, -52, 46,
   17, -22, -42, 35, -15, -41, 16, 34,
   31, -42, -19, -11, 55, 7, -39, 89,
   -11, -33, 20, -14, 22, 32, 3, -17,
   -6, 14, 34, 1, 55, -21, -90, -8,
   18, 27, 13, -29, 21, 15, -33, -51,
   -9, -11, 4, -16, -18, 23, -4, -4,
   48, 1, 7, 29, -14, -12, -16, 17,
   35, 8, 0, -7, -2, 9, 8, 17,
   -6, 53, -32, -21, -50, 5, 99, -60,
   -5, -53, 10, -31, 12, -5, 7, 80,
   36, 18, -31, 9, 98, 36, -63, -35,
   4, -13, -28, -24, 28, -13, 18, 16,
   -1, -18, -34, 10, 20, 7, 4, 29,
   11, 25, -7, 36, 14, 45, 24, 1,
   -16, 30, 6, 35, -6, -11, -24, 13,
   -1, 27, 39, 20, 48, -11, -4, -13,
   28, 11, -31, -18, 31, -29, 22, -2,
   -20, -16, 5, 30, -12, -28, -3, 93,
   -16, 23, 18, -29, 6, -54, -37, 28,
   -3, -3, -47, -3, -36, -55, -3, 41,
   -10, 47, -2, 23, 42, -7, -71, -27,
   83, -64, 7, -24, 8, 26, -17, 15,
   12, 31, -30, -38, -13, -33, -56, 4,
   -17, 20, 18, 1, -30, -5, -6, -31,
   -14, -37, 0, 22, 10, -30, 37, -17,
   18, 6, 5, 23, -36, -32, 14, 18,
   -13, -61, -52, -69, 44, -30, 16, 18,
   -4, -25, 14, 81, 26, -8, -23, -59,
   52, -104, 17, 119, -32, 26, 17, 1,
   23, 45, 29, -64, -57, -14, 73, 21,
   -13, -13, 9, -68, -7, -52, 3, 24,
   -39, 44, -15, 27, 14, 19, -9, -28,
   -11, 5, 3, -34, -2, 2, 22, -6,
   -23, 4, 3, 13, -22, -13, -10, -18,
   29, 6, 44, -13, -24, -8, 2, 30,
   14, 43, 6, 17, -73, -6, -7, 20,
   -80, -7, -7, -28, 15, -69, -38, -5,
   -100, -35, 15, -79, 23, 29, -18, -27,
   21, -66, -37, 8, -22, -39, 48, 4,
   -13, 1, -9, 11, -29, 22, 6, -49,
   32, -14, 47, -18, -4, 44, -52, -74,
   43, 30, 23, -14, 5, 0, -27, 4,
   -7, 10, -4, 10, 1, -16, 11, -18,
   -2, -5, 2, -11, 0, -20, -4, 38,
   74, 59, 39, 64, -10, 26, -3, -40,
   -68, 3, -30, -51, 8, -19, -27, -46,
   51, 52, 54, 36, 90, 92, 14, 13,
   -5, 0, 16, -62, 16, 11, -47, -37,
   -6, -5, 21, 54, -57, 32, 42, -6,
   62, -9, 16, 21, 24, 9, -10, -4,
   33, 50, 13, -15, 1, -35, -48, 18,
   -11, -17, -67, -13, 21, 38, -44, 36,
   -16, 29, 17, 5, -10, 18, 17, -32,
   2, 8, 22, -56, -15, -32, 40, 43,
   19, 46, -7, -100, -96, 19, 53, 24,
   21, -26, -48, -101, -82, 61, 38, -85,
   -28, -34, -1, 63, -5, -5, 39, 39,
   -38, 32, -12, -28, 20, 40, -8, 2,
   31, 12, -35, -13, 20, -25, 30, 8,
   3, -13, -9, -20, 2, -13, 24, 37,
   -10, 33, 6, 20, -16, -24, -6, -6,
   -19, -5, 22, 21, 10, 11, -4, -39,
   -1, 6, 49, 41, -15, -57, 21, -62,
   77, -69, -13, 0, -74, 1, -7, -38,
   -8, 6, 63, 28, 4, 26, -52, 82,
   63, 13, 45, -33, 44, -52, -65, -21,
   -46, -49, 64, -17, 32, 24, 68, -39,
   -16, -5, -26, 28, 5, -61, -28, 2,
   24, 11, -12, -33, 9, -37, -3, -28,
   22, -37, -12, 19, 0, -18, -2, 14,
   1, 4, 8, -9, -2, 43, -17, -2,
   -66, -31, 56, -40, -87, -36, -2, -4,
   -42, -45, -1, 31, -43, -15, 27, 63,
   -11, 32, -10, -33, 27, -19, 4, 15,
   -26, -34, 29, -4, -39, -65, 14, -20,
   -21, -17, -36, 13, 59, 47, -38, -33,
   13, -37, -8, -37, -7, -6, -76, -31,
   -12, -46, 7, 24, -21, -30, -14, 9,
   15, -12, -13, 47, -27, -25, -1, -39,
   0, 20, -9, 6, 7, 4, 3, 7,
   39, 50, 22, -7, 14, -20, 1, 70,
   -28, 29, -41, 10, -16, -5, -28, -2,
   -37, 32, -18, 17, 62, -11, -20, -50,
   36, 21, -62, -12, -56, 52, 50, 17,
   3, 48, 44, -41, -25, 3, 16, -3,
   0, 33, -6, 15, 27, 34, -25, 22,
   9, 17, -11, 36, 16, -2, 12, 21,
   -52, 45, -2, -10, 46, 21, -18, 67,
   -28, -13, 30, 37, 42, 16, -9, 11,
   75, 7, -64, -40, -10, 29, 57, -23,
   5, 53, -77, 3, -17, -5, 47, -55,
   -35, -36, -13, 52, -53, -71, 52, -111,
   -23, -26, -28, 29, -43, 55, -19, 43,
   -19, 54, -12, -33, -44, -39, -19, -10,
   -31, -10, 21, 38, -57, -20, 2, -25,
   8, -6, 50, 12, 15, 25, -25, 15,
   -30, -6, 9, 25, 37, 19, -4, 31,
   -22, 2, 4, 2, 36, 7, 3, -34,
   -80, 36, -10, -2, -5, 31, -36, 49,
   -70, 20, -36, 21, 24, 25, -46, -51,
   36, -58, -48, -40, -10, 55, 71, 47,
   10, -1, 1, 2, -46, -68, 16, 13,
   0, -74, -29, 73, -52, -18, -11, 7,
   -44, -82, -32, -70, -28, -1, -39, -68,
   -6, -41, 12, -22, -16, 40, -11, -25,
   51, -9, 21, 4, 4, -34, 7, -78,
   16, 6, -38, -30, -2, -44, 32, 0,
   22, 64, 5, -72, -2, -14, -10, -16,
   -8, -25, 12, 102, -58, 37, -10, -23,
   15, 49, 7, -7, 2, -20, -32, 45,
   -6, 48, 28, 30, 33, -1, 22, -6,
   30, 65, -17, 29, 74, 37, -26, -10,
   15, -24, 19, -66, 22, -10, -31, -1,
   -18, -9, 11, 37, -4, 45, 5, 41,
   17, 1, 1, 24, -58, 41, 5, -51,
   14, 8, 43, 16, -10, -1, 45, 32,
   -64, 3, -33, -25, -3, -27, -68, 12,
   23, -11, -13, -37, -40, 4, -21, -12,
   32, -23, -19, 76, 41, -23, -24, -44,
   -65, -1, -15, 1, 71, 63, 5, 20,
   -3, 21, -23, 31, -32, 18, -2, 27,
   31, 46, -5, -39, -5, -35, 18, -18,
   -40, -10, 3, 12, 2, -2, -22, 40,
   5, -6, 60, 36, 3, 29, -27, 10,
   25, -54, 5, 26, 39, 35, -24, -37,
   30, -91, 28, -4, -21, -27, -39, -6,
   5, 12, -128, 38, -16, 29, -95, -29,
   82, -2, 35, 2, 12, 8, -22, 10,
   80, -47, 2, -25, -73, -79, 16, -30,
   -32, -66, 48, 21, -45, -11, -47, 14,
   -27, -17, -7, 15, -44, -14, -44, -26,
   -32, 26, -23, 17, -7, -28, 26, -6,
   28, 6, -26, 2, 13, -14, -23, -14,
   19, 46, 16, 2, -33, -21, 28, -17,
   -42, 44, -37, 1, -39, 28, 84, -46,
   15, 10, 13, -44, 72, -26, 26, 32,
   -28, -12, -83, 2, 10, -30, -44, -10,
   -28, 53, 45, 65, 0, -25, 57, 36,
   -33, 6, 29, 44, -53, 11, 19, -2,
   -27, 35, 32, 49, 4, 23, 38, 36,
   24, 10, 51, -39, 4, -7, 26, 37,
   -35, 11, -47, -18, 28, 16, -35, 42,
   17, -21, -41, 28, 14, -12, 11, -45,
   7, -43, -15, 18, -5, 38, -40, -50,
   -30, -21, 9, -98, 13, 12, 23, 75,
   -56, -7, -3, -4, -1, -34, 12, -49,
   11, 26, -18, -28, -17, 33, 13, -14,
   40, 24, -72, -37, 10, 17, -6, 22,
   16, 16, -6, -12, -30, -14, 10, 40,
   -23, 12, 15, -3, -15, 13, -56, -4,
   -30, 1, -3, -17, 27, 50, -5, 64,
   -36, -19, 7, 29, 22, 25, 9, -16,
   -58, -69, -40, -61, -71, -14, 42, 93,
   26, 11, -6, -58, -11, 70, -52, 19,
   9, -30, -33, 11, -37, -47, -21, -22,
   -40, 10, 47, 4, -23, 17, 48, 41,
   -48, 14, 10, 15, 34, -23, -2, -47,
   23, -32, -13, -10, -26, -26, -4, 16,
   38, -14, 0, -12, -7, -7, 20, 44,
   -1, -32, -27, -16, 4, -6, -18, 14,
   5, 4, -29, 28, 7, -7, 15, -11,
   -20, -45, -36, 16, 84, 34, -59, -30,
   22, 126, 8, 68, 79, -17, 21, -68,
   37, 5, 15, 63, 49, 127, -90, 85,
   43, 7, 16, 9, 6, -45, -57, -43,
   57, 11, -23, -11, -29, 60, -26, 0,
   7, 42, -24, 10, 23, -25, 8, -7,
   -40, 19, -17, 35, 4, 27, -39, -91,
   27, -36, 34, 2, 16, -24, 25, 7,
   -21, 5, 17, 10, -22, -30, 9, -17,
   -61, -26, 33, 21, 58, -51, -14, 69,
   -38, 20, 7, 80, -4, -65, -6, -27,
   53, -12, 47, -1, -15, 1, 60, 102,
   -79, -4, 12, 9, 22, 37, -8, -4,
   37, 2, -3, -15, -16, -11, -5, 19,
   -6, -43, 20, -25, -18, 10, -27, 0,
   -28, -27, -11, 10, -18, -2, -4, -16,
   26, 14, -6, 7, -6, 1, 53, -2,
   -29, 23, 9, -30, -6, -4, -6, 56,
   70, 0, -33, -20, -17, -9, -24, 46,
   -5, -105, 47, -46, -51, 20, 20, -53,
   -81, -1, -7, 75, -5, -21, -65, 12,
   -52, 22, -50, -12, 49, 54, 76, -81,
   10, 45, -41, -59, 18, -19, 25, 14,
   -31, -53, -5, 12, 31, 84, -23, 2,
   7, 2, 10, -32, 39, -2, -12, 1,
   -9, 0, -10, -11, 9, 15, -8, -2,
   2, -1, 10, 14, -5, -40, 19, -7,
   -7, 26, -4, 2, 1, -27, 35, 32,
   21, -31, 26, 43, -9, 4, -32, 40,
   -62, -52, 36, 22, 38, 22, 36, -96,
   6, -10, -23, -49, 15, -33, -18, -3,
   0, 41, 21, -19, 21, 23, -39, -23,
   -6, 6, 47, 56, 4, 74, 0, -98,
   29, -47, -14, -36, 21, -22, 22, 16,
   13, 12, 16, -5, 13, 17, -13, -15,
   1, -34, -26, 26, 12, 32, 27, 13,
   -67, 27, 2, 8, 10, 18, 16, 20,
   -17, -17, 57, -64, 5, 14, 19, 31,
   -18, -44, -46, -16, 4, -25, 17, -126,
   -24, 39, 4, 8, 55, -25, -34, 39,
   -16, 3, 9, 71, 72, -31, -55, 6,
   10, -25, 32, -85, -21, 18, -8, 15,
   12, -27, -7, 1, -21, -2, -5, 48,
   -16, 18, 1, -22, -26, 16, 14, -31,
   27, -6, -15, -21, 4, -14, 18, -36
};

static const opus_int8 layer1_recur_weights[1728] = {
   20, 67, -99, 12, 41, -25, 49, -44,
   35, 81, 110, 47, 34, -66, -14, 14,
   -60, 34, 29, -73, 10, 41, 35, 89,
   7, -35, 22, 7, 27, -20, -6, 56,
   26, 66, 6, 33, -55, 53, 1, -21,
   14, 17, 68, 55, 59, 0, 18, -9,
   5, -41, 6, -5, -114, -12, 29, 42,
   -23, 10, 81, -27, 20, -53, -30, -62,
   40, 95, 25, -4, 3, 18, -8, -15,
   -29, -82, 2, -57, -3, -61, -29, -29,
   49, 2, -55, 5, -69, -99, -49, -51,
   6, -25, 12, 89, 44, -33, 5, 41,
   1, 23, -37, -37, -28, -48, 3, 4,
   -41, -30, -57, -35, -39, -1, -13, -56,
   -5, 50, 49, 41, -4, -4, 33, -22,
   -1, 33, 34, 18, 40, -42, 12, 1,
   -6, -2, 18, 17, 39, 44, 11, 65,
   -60, -45, 10, 91, 21, 9, -62, -11,
   8, 69, 37, 24, -30, 21, 26, -27,
   1, -28, 24, 66, -8, 6, -71, 34,
   24, 44, 58, -78, -19, 57, 17, -60,
   1, 12, -3, -1, -40, 22, 11, -5,
   25, 12, 1, 72, 79, 7, -50, 23,
   18, 13, 21, -11, -20, 5, 77, -94,
   24, 15, 57, -51, 3, 36, 53, -1,
   4, 14, 30, -31, 22, 40, 32, -11,
   -34, -36, -59, 58, 25, 21, -54, -23,
   40, 46, 18, 0, 12, 54, -96, -99,
   -59, 5, 119, -38, 50, 55, 12, -16,
   67, 0, 34, 35, 39, 35, -1, 69,
   24, 27, -30, -35, -4, -70, 2, -44,
   -7, -6, 19, -9, 60, 44, -21, -10,
   37, 43, -16, -3, 30, -15, -65, 31,
   -55, 18, -98, 76, 64, 25, 24, -18,
   -7, -68, -10, 38, 27, -60, 36, 33,
   16, 30, 34, -39, -37, 31, 12, 53,
   -54, 14, -26, -49, -128, -13, -5, -22,
   -11, -85, 55, -8, -51, -11, -33, -10,
   -31, -76, -41, 23, 44, -40, -54, -127,
   -101, 19, -23, -15, 15, 27, 58, -60,
   8, 14, -33, 1, 48, -9, -11, -123,
   3, 53, 23, 4, -28, 22, 2, -29,
   -67, 36, 12, 7, 55, -21, 88, 20,
   -1, -21, -17, 3, 41, 32, -10, -14,
   -5, -57, 67, 57, 21, 23, -2, -27,
   -73, -24, 120, 21, 18, -35, 42, -7,
   3, -45, -25, 76, -34, 50, 11, -54,
   -91, 3, -113, -20, -5, 47, 15, -47,
   17, 27, -3, -26, -7, 10, 7, 74,
   -40, 64, -7, -5, -24, -49, -24, -3,
   -10, 27, -17, -8, -3, 14, -27, 33,
   13, 39, 28, -7, -38, 29, 16, 44,
   19, 55, -3, 9, -13, -57, 43, 43,
   31, 0, -93, -17, 19, -56, 4, -12,
   -25, 37, -85, -13, -118, 33, -17, 56,
   71, -80, -4, 6, -11, -18, 47, -52,
   25, 9, 48, -107, 1, 21, 20, -3,
   10, -16, -4, 24, 17, 31, -61, -18,
   -50, 24, -10, 12, 71, 26, 11, -3,
   4, 1, 0, -7, -40, 18, 38, -34,
   38, 17, 8, -34, 2, 21, 123, -32,
   -26, 43, 14, -34, -1, -9, 37, -16,
   6, -17, -62, 68, 22, 17, 11, -75,
   33, -80, 62, -9, -75, 76, 36, -41,
   -8, -40, -11, -71, 40, -39, 62, -49,
   -81, 16, -9, -52, 52, 61, 17, -103,
   -27, -10, -8, -54, -57, 21, 23, -16,
   -52, 36, 18, 10, -5, 8, 15, -29,
   5, -19, -37, 8, -53, 6, 19, -37,
   38, -17, 48, 10, 0, 81, 46, 70,
   -29, 101, 11, 44, -44, -3, 24, 11,
   3, 14, -9, 11, 14, -45, 13, 46,
   -3, -57, 68, 44, 63, 98, 25, -28,
   -23, 15, 32, -10, 53, -6, -2, -9,
   -6, 16, -107, -11, -11, -28, 59, 57,
   -22, 38, 42, 83, 27, 5, 29, -30,
   12, -21, -13, 31, 38, -21, 58, -10,
   -10, -15, -2, -5, 11, 12, -73, -28,
   -38, 22, 2, -25, 73, -52, -12, -55,
   32, -63, 21, 51, 33, 52, -26, 55,
   -26, -26, 57, -32, -4, -52, -61, 21,
   -33, -91, -51, 69, -90, -53, -38, -44,
   12, -76, -20, 77, -45, -7, 86, 43,
   -109, -33, -105, -40, -121, -10, 0, -72,
   45, -51, -75, -49, -38, -1, -62, 18,
   -1, 30, -44, -14, -10, -67, 40, -10,
   -34, 46, -64, -32, 29, -13, 33, 3,
   -32, -5, 28, -27, -25, 93, 24, 68,
   -40, 57, 23, -3, -21, -58, 17, -39,
   -17, -22, -89, 11, 18, -46, 27, 24,
   46, 127, 61, 87, 31, 127, -36, 47,
   -23, 47, 127, -24, 110, 122, 30, 100,
   0, 96, -12, 6, 50, 44, -13, 73,
   4, 55, -11, -15, 49, 42, -6, 20,
   -35, 58, 18, 38, 42, 72, 19, -21,
   11, 9, -37, 7, 29, 31, 16, -17,
   13, -50, 19, 5, -23, 51, -16, -5,
   4, -24, 76, 10, -53, -28, -7, -65,
   74, 40, -16, -29, 32, -16, -49, -35,
   -3, 59, -96, -50, -43, -43, -61, -15,
   -8, -36, -34, -33, -14, 11, -3, -39,
   4, -114, -123, -11, -49, -21, 14, -56,
   1, 43, -63, 26, 40, 18, -10, -26,
   -14, -15, -35, -35, -11, 32, -44, -67,
   2, 22, 7, 3, -9, -30, -51, -28,
   28, 6, -22, 16, 34, -25, -52, -54,
   -8, -6, 5, 8, 20, -16, -17, -44,
   27, 3, 31, -5, -48, -1, -3, 116,
   11, 71, -31, -47, 109, 50, -22, -12,
   -57, 32, 66, 8, -25, -93, -54, -10,
   19, -76, -34, 97, 48, -36, -18, -30,
   -39, -26, -12, 28, 14, 12, -12, -31,
   38, 2, 10, 4, -40, 20, 16, -61,
   2, 64, 39, 5, 15, 33, 40, -61,
   -49, 93, -10, 33, 28, -11, -27, -18,
   39, -62, -6, -6, 62, 11, -8, 38,
   -67, 12, 27, 39, -27, 123, -18, -6,
   -65, 83, -64, 20, 19, -11, 33, 24,
   17, 56, 78, 7, -15, 54, -101, -9,
   115, -96, 50, 51, 35, 34, 27, 37,
   -40, -11, 8, -36, 42, -45, 2, -23,
   0, 67, -8, -9, -13, 50, -14, -27,
   4, 0, -8, -14, 30, -9, 29, 15,
   9, -38, 37, -8, 50, -46, 54, 41,
   -11, -8, -11, -26, 39, 45, 14, -26,
   -17, -27, 69, 38, 39, 98, 66, 0,
   42, 123, -101, -19, -83, 117, -32, 56,
   10, 12, -88, 79, -53, 56, 63, 95,
   -62, 9, 36, -13, -79, -16, 37, -46,
   35, -34, 14, 17, -54, 5, 21, -7,
   7, 63, 56, 15, 27, -76, -25, 4,
   -26, -63, 28, -67, -52, 43, -47, -70,
   40, -12, 40, -66, -37, 0, 35, 37,
   -53, 4, -17, -51, 11, 21, 14, -34,
   -4, 24, -42, 29, 22, 7, 28, 12,
   37, 39, -39, -19, 65, -60, -50, -2,
   1, 82, 39, 19, -23, -43, -22, -67,
   -35, -34, 32, 102, 81, 127, 36, 67,
   -45, 1, -67, -52, -4, 35, 20, 28,
   71, 86, -35, -9, -83, -34, 12, 9,
   -23, 2, 14, 28, -23, 7, -25, 45,
   7, 17, -37, 0, -19, 31, 26, 40,
   -27, -16, 17, 5, -21, 23, 24, 96,
   -55, 52, -19, -14, -6, 1, 50, -34,
   86, -53, 38, 2, -52, -36, -13, 60,
   -85, -120, 32, 7, -12, 22, 70, -7,
   -94, 38, -76, -31, -20, 15, -28, 7,
   6, 40, 53, 88, 3, 38, 18, -8,
   -22, -23, 51, 37, -9, 13, -32, 25,
   -21, 27, 31, 20, 18, -9, -13, 1,
   21, -24, -13, 39, 15, -11, -29, -36,
   18, 15, 8, 27, 21, -94, -1, -22,
   49, 66, -1, 6, -3, -40, -18, 6,
   28, 12, 33, -59, 62, 60, -48, 90,
   -1, 108, 9, 18, -2, 27, 77, -65,
   82, -48, -38, -19, -11, 127, 50, 66,
   18, -13, -22, 60, -38, 40, -14, -26,
   -13, 38, 67, 57, 30, 33, 26, 36,
   38, -17, 27, -28, 20, 12, -64, 18,
   5, -33, -27, 13, -26, 32, 35, -5,
   -48, -14, 92, 43, -47, -14, 40, 11,
   51, 66, 22, -63, -16, -61, 4, -28,
   27, 20, -33, -30, -21, -29, -53, 31,
   -40, 24, 43, -4, -19, 21, 67, 20,
   100, -16, -93, 78, -6, -18, -52, -37,
   -9, 66, -31, -8, 26, 18, 4, 24,
   -22, 17, -2, -13, 27, 0, 8, -18,
   -25, 5, -21, -24, -7, 18, -93, 21,
   7, 2, -75, 69, 50, -5, -15, -17,
   60, -42, 55, 1, -4, 3, 10, 46,
   16, -13, 45, -7, -10, -44, -108, 49,
   2, -15, -64, -12, -72, 32, -38, -45,
   10, -54, 13, -13, -27, -36, -64, 58,
   -62, -101, 88, -86, -71, -39, -9, -128,
   32, 15, -4, 54, -16, -39, -26, -36,
   46, 48, -64, -10, 19, 30, -13, 34,
   -8, 50, 60, -22, -6, -11, -30, 5,
   50, 32, 56, 0, 25, 6, 68, 11,
   -29, 45, -9, -12, 4, 1, 18, -49,
   0, -38, -19, 90, 29, 35, 51, 8,
   -48, 96, -1, -12, -9, -32, -63, -65,
   -7, 38, 89, 28, -85, -28, -23, -25,
   -128, 56, 79, -36, 99, -6, -37, 7,
   -13, -69, -46, -29, 25, 64, -21, 17,
   1, 42, -66, 1, 80, 26, -32, 21,
   15, 15, 6, 6, -10, 15, 127, 5,
   38, 27, 87, -57, -25, 11, 72, -21,
   -5, 11, -13, -66, 78, 36, -3, 41,
   -21, 8, -33, 23, 73, 28, 57, -25,
   -5, 4, -22, -47, 15, 4, -57, -72,
   33, 1, 18, 2, 53, -71, -99, -21,
   -3, -111, 108, 71, -14, 82, 25, 61,
   -48, 5, 9, -51, -20, -25, -3, 14,
   -33, 14, -3, -34, 22, 12, -19, -38,
   -16, 2, 21, 16, 26, -31, 75, 44,
   -31, 16, 26, 66, 17, -9, -22, -22,
   22, -44, 22, 27, 2, 58, -14, 10,
   -73, -42, 55, -25, -61, 72, -1, 30,
   -58, -25, 63, 26, -48, -40, 26, -30,
   60, 8, -17, -1, -18, -20, 43, -20,
   -4, -28, 127, -106, 29, 70, 64, -27,
   39, -33, -5, -88, -40, -52, 26, 44,
   -17, 23, 2, -49, 22, -9, -8, 86,
   49, -43, -60, 1, 10, 45, 36, -53,
   -4, 33, 38, 48, -72, 1, 19, 21,
   -65, 4, -5, -62, 27, -25, 17, -6,
   6, -45, -39, -46, 4, 26, 127, -9,
   18, -33, -18, -3, 33, 2, -5, 15,
   -26, -22, -117, -63, -17, -59, 61, -74,
   7, -47, -58, -128, -67, 15, -16, -128,
   12, 2, 20, 9, -48, -40, 43, 3,
   -40, -16, -38, -6, -22, -28, -16, -59,
   -22, 6, -5, 11, -12, -66, -40, 27,
   -62, -44, -19, 38, -3, 39, -8, 40,
   -24, 13, 21, 50, -60, -22, 53, -29,
   -6, 1, 22, -59, 0, 17, -39, 115
};

static const opus_int8 layer1_bias[72] = {
   -42, 20, 16, 0, 105, 60, 1, -97,
   24, 60, 18, 13, 62, 25, 127, 34,
   79, 55, 118, 127, 95, 31, -4, 87,
   21, 12, 2, -14, 18, 23, 8, 17,
   -1, -8, 5, 4, 24, 37, 21, 13,
   36, 13, 17, 18, 37, 30, 33, 1,
   8, -16, -11, -5, -31, -3, -5, 0,
   6, 3, 58, -7, -1, -16, 5, -13,
   16, 10, -2, -14, 11, -4, 3, -11
};

static const opus_int8 layer2_weights[48] = {
   -113, -88, 31, -128, -126, -61, 85, -35,
   118, -128, -61, 127, -128, -17, -128, 127,
   104, -9, -128, 33, 45, 127, 5, 83,
   84, -128, -85, -128, -45, 48, -53, -128,
   46, 127, -17, 125, 117, -41, -117, -91,
   -127, -68, -1, -89, -80, 32, 106, 7
};

static const opus_int8 layer2_bias[2] = {
   14, 117
};

const AnalysisDenseLayer layer0 = {
   layer0_bias,
   layer0_weights,
   25, 32, 0
};

const AnalysisGRULayer layer1 = {
   layer1_bias,
   layer1_weights,
   layer1_recur_weights,
   32, 24
};

const AnalysisDenseLayer layer2 = {
   layer2_bias,
   layer2_weights,
   24, 2, 1
};

