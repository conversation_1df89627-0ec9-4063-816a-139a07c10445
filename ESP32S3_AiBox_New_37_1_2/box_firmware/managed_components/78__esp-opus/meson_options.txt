# Optimizations
option('fixed-point', type : 'boolean', value : false, description : 'Compile without floating point (for machines without a fast enough FPU')
option('fixed-point-debug', type : 'boolean', value : false, description : 'Debug fixed-point implementation')
option('float-api', type : 'boolean', value : true, description : 'Compile with or without the floating point API (for machines with no float library')
option('float-approx', type : 'boolean', value : false, description : 'Enable fast approximations for floating point (not supported on all platforms)')
option('rtcd', type : 'feature', value : 'auto', description : 'Run-time CPU capabilities detection')
option('asm', type : 'feature', value : 'auto', description : 'Assembly optimizations for ARM (fixed-point)')
option('intrinsics', type : 'feature', value : 'auto', description : 'Intrinsics optimizations for ARM NEON or x86')

option('deep-plc', type : 'feature', value : 'disabled', description : 'Enable Deep Packet Loss Concealment (PLC)')
option('dred', type : 'feature', value : 'disabled', description : 'Enable Deep Redundancy (DRED)')
option('osce', type : 'feature', value : 'disabled', description : 'Enable Opus Speech Coding Enhancement (OSCE)')
option('dnn-debug-float', type : 'feature', value : 'disabled', description : 'Compute DNN using float weights')

option('custom-modes', type : 'boolean', value : false, description : 'Enable non-Opus modes, e.g. 44.1 kHz & 2^n frames')
option('extra-programs', type : 'feature', value : 'auto', description : 'Extra programs (demo and tests)')
option('assertions', type : 'boolean', value : false, description : 'Additional software error checking')
option('hardening', type : 'boolean', value : true, description : 'Run-time checks that are cheap and safe for use in production')
option('fuzzing', type : 'boolean', value : false, description : 'Causes the encoder to make random decisions')
option('check-asm', type : 'boolean', value : false, description : 'Run bit-exactness checks between optimized and c implementations')

# common feature options
option('tests', type : 'feature', value : 'auto', description : 'Build tests')
option('docs', type: 'feature', value: 'auto', description: 'Build API documentation')

# other options
option('docdir', type: 'string', value: 'doc/opus', description: 'Directory to install documentation into (default: DATADIR/doc/opus')
